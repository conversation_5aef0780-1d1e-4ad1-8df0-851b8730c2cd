#!/usr/bin/env python3
"""
基于融合双层注意力机制的MAPPO多AGV协同调度系统
Multi-Agent Proximal Policy Optimization with Dual-Layer Attention for AGV Coordination

根据研究方案实现完整的多AGV协同调度系统，包含：
1. 多AGV仓储环境建模
2. 双层注意力机制（任务分配注意力 + 协作感知注意力）
3. MAPPO框架集成
4. 训练策略优化
5. 性能评估与实验验证


"""

import numpy as np
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    import torch.optim as optim
    from torch.distributions import Categorical
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available, running in basic mode")

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Matplotlib not available, visualization disabled")
from collections import deque, namedtuple
import random
import math
import time
import json
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

# 设置设备和随机种子
if TORCH_AVAILABLE:
    # 检查GPU可用性并设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
        torch.cuda.manual_seed_all(42)
else:
    device = "cpu"
    print("PyTorch不可用，使用CPU模式")

np.random.seed(42)
random.seed(42)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ================================
# 第一部分：环境建模与基础框架
# ================================

class TaskStatus(Enum):
    """任务状态枚举"""
    UNASSIGNED = 0  # 未分配
    ASSIGNED = 1    # 已分配
    COMPLETED = 2   # 已完成

class AGVStatus(Enum):
    """AGV状态枚举"""
    IDLE = 0        # 空闲
    MOVING = 1      # 移动中
    LOADING = 2     # 装载中
    UNLOADING = 3   # 卸载中

@dataclass
class Task:
    """任务实体类"""
    id: int
    x: int
    y: int
    weight: int  # 5 或 10
    status: TaskStatus = TaskStatus.UNASSIGNED
    assigned_agv: Optional[int] = None
    priority: float = 1.0
    deadline: float = float('inf')
    
    def get_normalized_features(self, map_width: int, map_height: int, agv_positions: List[Tuple[int, int]]) -> np.ndarray:
        """获取归一化的5维特征向量"""
        # 位置归一化
        x_norm = self.x / map_width
        y_norm = self.y / map_height
        
        # 重量归一化 (5->0, 10->1)
        w_norm = (self.weight - 5) / 5
        
        # 状态
        s = self.status.value
        
        # 到所有AGV的最短曼哈顿距离归一化
        if agv_positions:
            min_dist = min(abs(self.x - agv_x) + abs(self.y - agv_y) 
                          for agv_x, agv_y in agv_positions)
            d_min = min_dist / (map_width + map_height)
        else:
            d_min = 1.0
            
        return np.array([x_norm, y_norm, w_norm, s, d_min], dtype=np.float32)

@dataclass
class AGV:
    """AGV实体类"""
    id: int
    x: int
    y: int
    capacity: int = 25
    current_load: int = 0
    task_queue: List[int] = None
    target_task: Optional[int] = None
    status: AGVStatus = AGVStatus.IDLE
    
    def __post_init__(self):
        if self.task_queue is None:
            self.task_queue = []
    
    def get_normalized_features(self, map_width: int, map_height: int, max_queue_length: int = 4) -> np.ndarray:
        """获取归一化的6维特征向量"""
        # 位置归一化
        x_norm = self.x / map_width
        y_norm = self.y / map_height
        
        # 载重归一化
        l_norm = self.current_load / self.capacity
        
        # 任务队列长度归一化
        q_norm = len(self.task_queue) / max_queue_length
        
        # 目标任务ID归一化 (假设最大任务数为16)
        target_norm = self.target_task / 15 if self.target_task is not None else -1
        
        # 是否空闲
        idle = 1.0 if self.status == AGVStatus.IDLE else 0.0
        
        return np.array([x_norm, y_norm, l_norm, q_norm, target_norm, idle], dtype=np.float32)
    
    def can_take_task(self, task_weight: int) -> bool:
        """检查是否可以承担任务"""
        return self.current_load + task_weight <= self.capacity
    
    def move_to(self, x: int, y: int):
        """移动到指定位置"""
        self.x, self.y = x, y
        self.status = AGVStatus.MOVING

class WarehouseEnvironment:
    """26×10网格仓储环境"""
    
    def __init__(self):
        self.width = 26
        self.height = 10
        self.num_agvs = 4
        self.num_tasks = 16
        self.num_shelves = 15
        
        # 初始化地图 (0: 空地, 1: 货架, 2: AGV, 3: 任务)
        self.grid = np.zeros((self.height, self.width), dtype=int)
        
        # 初始化货架布局 (3行5列，每个货架4×2)
        self._setup_shelves()
        
        # 初始化AGV和任务
        self.agvs = []
        self.tasks = []
        self.reset()
        
        # 性能统计
        self.total_timesteps = 0
        self.completed_tasks = 0
        self.total_collisions = 0
        self.total_path_length = 0
        
    def _setup_shelves(self):
        """设置15个货架的布局 (3行5列)"""
        shelf_id = 1
        shelf_positions_debug = []
        out_of_bounds = []

        for row in range(3):
            for col in range(5):
                # 每个货架4×2，货架间有1格通道
                start_x = col * 5 + 1  # 5 = 4(货架宽度) + 1(通道)
                start_y = row * 3 + 1  # 3 = 2(货架高度) + 1(通道)

                print(f"货架 {shelf_id}: 起始位置({start_x}, {start_y})")

                # 放置4×2货架
                for dy in range(2):
                    for dx in range(4):
                        x, y = start_x + dx, start_y + dy
                        if x < self.width and y < self.height:
                            self.grid[y, x] = 1
                            shelf_positions_debug.append((x, y))
                        else:
                            out_of_bounds.append((x, y))
                shelf_id += 1

        # 调试信息
        print(f"货架布局调试: 放置了{len(shelf_positions_debug)}个货架格子")
        print(f"超出边界的位置: {len(out_of_bounds)}个")
        if out_of_bounds:
            print(f"超出边界的位置: {out_of_bounds[:5]}...")
        print(f"前10个货架位置: {shelf_positions_debug[:10]}")
    
    def reset(self) -> Dict:
        """重置环境"""
        # 清除AGV和任务标记
        self.grid[self.grid >= 2] = 0
        
        # 初始化AGV（确保在通道中）
        self.agvs = []
        agv_positions = [(0, 0), (5, 0), (10, 0), (15, 0)]  # 在通道中初始化
        for i, (x, y) in enumerate(agv_positions):
            # 确保AGV位置不在货架上
            if self.grid[y, x] == 1:
                print(f"警告: AGV {i} 初始位置({x}, {y})与货架冲突!")
                # 寻找附近的空位置
                for dx in [-1, 1, 0]:
                    for dy in [-1, 1, 0]:
                        new_x, new_y = x + dx, y + dy
                        if (0 <= new_x < self.width and 0 <= new_y < self.height and
                            self.grid[new_y, new_x] == 0):
                            x, y = new_x, new_y
                            break
                    else:
                        continue
                    break

            agv = AGV(id=i, x=x, y=y)
            self.agvs.append(agv)
            self.grid[y, x] = 2
        
        # 初始化任务
        self.tasks = []
        task_positions = self._generate_task_positions()
        for i, (x, y) in enumerate(task_positions):
            weight = random.choice([5, 10])
            task = Task(id=i, x=x, y=y, weight=weight)
            self.tasks.append(task)
            # 不修改grid值，保持货架标记为1，任务信息存储在task对象中
        
        # 重置统计
        self.total_timesteps = 0
        self.completed_tasks = 0
        self.total_collisions = 0
        self.total_path_length = 0
        
        return self._get_observations()
    
    def _generate_task_positions(self) -> List[Tuple[int, int]]:
        """生成16个任务位置（在货架上）"""
        positions = []

        # 收集所有货架位置
        shelf_positions = []
        for y in range(self.height):
            for x in range(self.width):
                if self.grid[y, x] == 1:  # 货架
                    shelf_positions.append((x, y))

        if len(shelf_positions) < self.num_tasks:
            raise ValueError(f"货架位置数量({len(shelf_positions)})少于任务数量({self.num_tasks})")

        # 从货架位置中随机选择任务位置
        selected_positions = random.sample(shelf_positions, self.num_tasks)

        return selected_positions
    
    def _get_observations(self) -> Dict:
        """获取观察状态"""
        # 获取AGV位置用于任务特征计算
        agv_positions = [(agv.x, agv.y) for agv in self.agvs]
        
        # 任务特征嵌入 (16 × 5 -> 16 × 64)
        task_features = []
        for task in self.tasks:
            features = task.get_normalized_features(self.width, self.height, agv_positions)
            task_features.append(features)
        task_features = np.array(task_features)
        
        # AGV特征嵌入 (4 × 6 -> 4 × 64)
        agv_features = []
        for agv in self.agvs:
            features = agv.get_normalized_features(self.width, self.height)
            agv_features.append(features)
        agv_features = np.array(agv_features)
        
        # 全局状态 (20 × 64: 4个AGV + 16个任务)
        # 注意：这里先返回原始特征，嵌入将在神经网络中完成
        global_state = {
            'agv_features': agv_features,      # (4, 6)
            'task_features': task_features,    # (16, 5)
            'grid': self.grid.copy()           # (10, 26)
        }
        
        # 局部观察（每个AGV的局部观察）
        local_observations = []
        for i, agv in enumerate(self.agvs):
            local_obs = self._get_local_observation(i)
            local_observations.append(local_obs)
        
        return {
            'global_state': global_state,
            'local_observations': local_observations
        }
    
    def _get_local_observation(self, agv_id: int) -> Dict:
        """获取单个AGV的局部观察"""
        agv = self.agvs[agv_id]
        
        # 可视范围内的任务（距离阈值内）
        visible_tasks = []
        for task in self.tasks:
            dist = abs(agv.x - task.x) + abs(agv.y - task.y)
            if dist <= 8:  # 可视范围阈值
                visible_tasks.append(task.get_normalized_features(
                    self.width, self.height, [(agv.x, agv.y) for agv in self.agvs]))
        
        # 附近的其他AGV
        nearby_agvs = []
        for other_agv in self.agvs:
            if other_agv.id != agv_id:
                nearby_agvs.append(other_agv.get_normalized_features(self.width, self.height))
        
        return {
            'self_features': agv.get_normalized_features(self.width, self.height),
            'visible_tasks': np.array(visible_tasks) if visible_tasks else np.zeros((0, 5)),
            'nearby_agvs': np.array(nearby_agvs)  # (3, 6)
        }

    def step(self, actions: List[Tuple[int, int]]) -> Tuple[Dict, List[float], bool, Dict]:
        """环境步进

        Args:
            actions: 每个AGV的动作 [(high_action, low_action), ...]
            high_action: 任务分配动作 (0: 保持, 1-16: 选择任务, 17: 等待)
            low_action: 运动控制动作 (0: 上, 1: 下, 2: 左, 3: 右, 4: 等待)

        Returns:
            observations, rewards, done, info
        """
        self.total_timesteps += 1
        rewards = []
        info = {'collisions': 0, 'completed_tasks': 0, 'invalid_actions': 0}

        # 处理每个AGV的动作
        for i, (high_action, low_action) in enumerate(actions):
            reward = self._process_agv_action(i, high_action, low_action, info)
            rewards.append(reward)

        # 检查任务完成情况
        done = self._check_episode_done()

        # 获取新的观察
        observations = self._get_observations()

        return observations, rewards, done, info

    def _process_agv_action(self, agv_id: int, high_action: int, low_action: int, info: Dict) -> float:
        """处理单个AGV的动作并计算多维度奖励"""
        agv = self.agvs[agv_id]

        # 处理高层动作（任务分配）
        task_allocation_reward = self._process_high_level_action(agv_id, high_action, info)

        # 处理低层动作（运动控制）
        movement_reward = self._process_low_level_action(agv_id, low_action, info)

        # 计算协作奖励
        collaboration_reward = self._calculate_collaboration_reward(agv_id, info)

        # 计算系统效率奖励
        system_reward = self._calculate_system_reward()

        # 总奖励
        total_reward = (task_allocation_reward + movement_reward +
                       collaboration_reward + system_reward / len(self.agvs))

        return total_reward

    def _process_high_level_action(self, agv_id: int, high_action: int, info: Dict) -> float:
        """处理高层任务分配动作"""
        agv = self.agvs[agv_id]
        reward = 0.0

        if high_action == 0:
            # 保持当前任务
            if agv.target_task is not None:
                reward += 0.1  # 持续执行任务的小奖励
            else:
                reward -= 0.1  # 没有任务时保持状态的惩罚

        elif 1 <= high_action <= 16:
            # 选择任务
            task_id = high_action - 1
            if task_id < len(self.tasks):
                task = self.tasks[task_id]
                if (task.status == TaskStatus.UNASSIGNED and
                    agv.can_take_task(task.weight)):

                    # 分配任务
                    task.status = TaskStatus.ASSIGNED
                    task.assigned_agv = agv_id
                    agv.target_task = task_id
                    agv.task_queue.append(task_id)

                    # 任务分配奖励（考虑优先级和距离）
                    distance = abs(agv.x - task.x) + abs(agv.y - task.y)
                    distance_factor = max(0.1, 1.0 - distance / (self.width + self.height))
                    priority_factor = 1.0 + 0.5 * task.priority

                    reward += 2.0 * distance_factor * priority_factor

                else:
                    reward -= 1.0  # 无效任务分配惩罚
                    info['invalid_actions'] += 1

        elif high_action == 17:
            # 进入等待状态
            agv.status = AGVStatus.IDLE
            # 如果有可用任务但选择等待，给予小惩罚
            available_tasks = sum(1 for task in self.tasks
                                if task.status == TaskStatus.UNASSIGNED and agv.can_take_task(task.weight))
            if available_tasks > 0:
                reward -= 0.2

        return reward

    def _process_low_level_action(self, agv_id: int, low_action: int, info: Dict) -> float:
        """处理低层运动控制动作"""
        agv = self.agvs[agv_id]
        reward = 0.0

        old_x, old_y = agv.x, agv.y
        new_x, new_y = old_x, old_y

        # 动作映射
        if low_action == 0:  # 上移
            new_y = max(0, old_y - 1)
        elif low_action == 1:  # 下移
            new_y = min(self.height - 1, old_y + 1)
        elif low_action == 2:  # 左移
            new_x = max(0, old_x - 1)
        elif low_action == 3:  # 右移
            new_x = min(self.width - 1, old_x + 1)
        elif low_action == 4:  # 等待
            pass

        # 检查移动有效性
        if self._is_valid_move(agv_id, new_x, new_y):
            # 更新AGV位置
            self.grid[old_y, old_x] = 0
            agv.move_to(new_x, new_y)
            self.grid[new_y, new_x] = 2

            # 移动效率奖励
            if new_x != old_x or new_y != old_y:
                # 基础移动成本
                alpha_move = 0.1
                reward -= alpha_move
                self.total_path_length += 1

                # 距离引导奖励
                if agv.target_task is not None:
                    task = self.tasks[agv.target_task]
                    old_dist = abs(old_x - task.x) + abs(old_y - task.y)
                    new_dist = abs(new_x - task.x) + abs(new_y - task.y)

                    if new_dist < old_dist:
                        reward += 0.2  # 接近目标奖励
                    elif new_dist > old_dist:
                        reward -= 0.15  # 远离目标惩罚

                    # 如果到达货架相邻位置（距离为1）
                    if new_dist == 1:
                        reward += self._complete_task(agv_id, info)

            else:
                # 等待成本
                alpha_idle = 0.05
                reward -= alpha_idle

                # 如果在货架相邻位置等待，检查是否可以完成任务
                if agv.target_task is not None:
                    task = self.tasks[agv.target_task]
                    distance = abs(agv.x - task.x) + abs(agv.y - task.y)
                    if distance == 1:
                        reward += self._complete_task(agv_id, info)
        else:
            reward -= 1.0  # 无效移动惩罚
            info['invalid_actions'] += 1

        return reward

    def _complete_task(self, agv_id: int, info: Dict) -> float:
        """完成任务并计算奖励"""
        agv = self.agvs[agv_id]
        if agv.target_task is None:
            return 0.0

        task = self.tasks[agv.target_task]
        if task.status != TaskStatus.ASSIGNED:
            return 0.0

        # 检查AGV是否在任务货架的相邻位置（曼哈顿距离为1）
        distance = abs(agv.x - task.x) + abs(agv.y - task.y)
        if distance != 1:
            return 0.0  # 必须在货架旁边才能完成任务

        # 完成任务
        task.status = TaskStatus.COMPLETED
        agv.current_load += task.weight
        agv.target_task = None
        if task.id in agv.task_queue:
            agv.task_queue.remove(task.id)

        self.completed_tasks += 1
        info['completed_tasks'] += 1

        # 任务完成奖励（考虑优先级和时间效率）
        w_priority = 1.0 + 0.5 * task.priority

        # 时间效率权重（简化计算）
        optimal_time = 10  # 假设的最优完成时间
        actual_time = self.total_timesteps
        w_time = max(0.1, 1.0 - (actual_time - optimal_time) / 100)

        completion_reward = 10.0 * w_priority * w_time

        return completion_reward

    def _calculate_collaboration_reward(self, agv_id: int, info: Dict) -> float:
        """计算协作奖励"""
        agv = self.agvs[agv_id]
        reward = 0.0

        # 碰撞避免奖励
        beta_avoid = 0.5
        beta_collision = 2.0

        collision_occurred = False
        collision_avoided = False

        # 检查与其他AGV的距离
        for other_agv in self.agvs:
            if other_agv.id != agv_id:
                distance = abs(agv.x - other_agv.x) + abs(agv.y - other_agv.y)

                if distance == 0:
                    # 发生碰撞
                    collision_occurred = True
                elif distance == 1:
                    # 成功避免碰撞
                    collision_avoided = True

        if collision_occurred:
            reward -= beta_collision
            info['collisions'] += 1
        elif collision_avoided:
            reward += beta_avoid

        return reward

    def _calculate_system_reward(self) -> float:
        """计算系统效率奖励"""
        reward = 0.0

        # 负载均衡奖励
        gamma_balance = 1.0
        loads = [agv.current_load for agv in self.agvs]
        if len(loads) > 1:
            mean_load = np.mean(loads)
            if mean_load > 0:
                load_variance = np.var(loads)
                balance_score = 1.0 - (load_variance / (mean_load ** 2))
                reward += gamma_balance * balance_score

        # 系统吞吐量奖励
        gamma_throughput = 0.1
        if self.total_timesteps > 0:
            throughput = self.completed_tasks / self.total_timesteps
            reward += gamma_throughput * throughput

        return reward

    def get_enhanced_action_masks(self) -> List[Tuple[np.ndarray, np.ndarray]]:
        """获取增强的动作掩码（考虑更多约束）"""
        masks = []

        for agv in self.agvs:
            # 高层动作掩码 (18维: 0保持 + 1-16任务 + 17等待)
            high_mask = np.ones(18, dtype=bool)

            # 检查任务分配的有效性
            for i, task in enumerate(self.tasks):
                task_valid = True

                # 基本可用性检查
                if task.status != TaskStatus.UNASSIGNED:
                    task_valid = False

                # 载重能力检查
                if not agv.can_take_task(task.weight):
                    task_valid = False

                # 距离合理性检查（避免选择过远的任务）
                distance = abs(agv.x - task.x) + abs(agv.y - task.y)
                max_reasonable_distance = (self.width + self.height) * 0.7
                if distance > max_reasonable_distance:
                    task_valid = False

                # 优先级检查（如果有更高优先级的近距离任务）
                for other_task in self.tasks:
                    if (other_task.status == TaskStatus.UNASSIGNED and
                        other_task.priority > task.priority and
                        agv.can_take_task(other_task.weight)):
                        other_distance = abs(agv.x - other_task.x) + abs(agv.y - other_task.y)
                        if other_distance < distance * 0.8:  # 其他任务明显更近且优先级更高
                            task_valid = False
                            break

                high_mask[i + 1] = task_valid

            # 低层动作掩码 (5维: 上下左右等待)
            low_mask = np.ones(5, dtype=bool)

            # 检查移动方向的有效性
            directions = [
                (agv.x, max(0, agv.y - 1)),      # 上
                (agv.x, min(self.height - 1, agv.y + 1)),  # 下
                (max(0, agv.x - 1), agv.y),      # 左
                (min(self.width - 1, agv.x + 1), agv.y)   # 右
            ]

            for i, (x, y) in enumerate(directions):
                if not self._is_valid_move(agv.id, x, y):
                    low_mask[i] = False

            # 智能移动建议：如果有目标任务，优先考虑朝目标方向移动
            if agv.target_task is not None:
                task = self.tasks[agv.target_task]
                dx = task.x - agv.x
                dy = task.y - agv.y

                # 如果不是朝目标方向移动，降低该方向的优先级（但不完全禁止）
                if dy > 0 and low_mask[1]:  # 应该向下
                    if low_mask[0]:  # 但向上是可行的
                        pass  # 保持掩码，让智能体学习
                if dy < 0 and low_mask[0]:  # 应该向上
                    if low_mask[1]:  # 但向下是可行的
                        pass
                if dx > 0 and low_mask[3]:  # 应该向右
                    if low_mask[2]:  # 但向左是可行的
                        pass
                if dx < 0 and low_mask[2]:  # 应该向左
                    if low_mask[3]:  # 但向右是可行的
                        pass

            masks.append((high_mask, low_mask))

        return masks

    def _is_valid_move(self, agv_id: int, x: int, y: int) -> bool:
        """检查移动是否有效"""
        # 边界检查
        if x < 0 or x >= self.width or y < 0 or y >= self.height:
            return False

        # 障碍物检查（货架）
        if self.grid[y, x] == 1:
            return False

        # 碰撞检查（其他AGV）
        for other_agv in self.agvs:
            if other_agv.id != agv_id and other_agv.x == x and other_agv.y == y:
                self.total_collisions += 1
                return False

        return True

    def _check_episode_done(self) -> bool:
        """检查回合是否结束"""
        # 所有任务完成或达到最大时间步
        completed = sum(1 for task in self.tasks if task.status == TaskStatus.COMPLETED)
        return completed == len(self.tasks) or self.total_timesteps >= 1000

    def get_action_masks(self) -> List[Tuple[np.ndarray, np.ndarray]]:
        """获取每个AGV的动作掩码"""
        masks = []
        for agv in self.agvs:
            # 高层动作掩码 (18维: 0保持 + 1-16任务 + 17等待)
            high_mask = np.ones(18, dtype=bool)

            # 检查任务分配的有效性
            for i, task in enumerate(self.tasks):
                if (task.status != TaskStatus.UNASSIGNED or
                    not agv.can_take_task(task.weight)):
                    high_mask[i + 1] = False  # 任务i+1无效

            # 低层动作掩码 (5维: 上下左右等待)
            low_mask = np.ones(5, dtype=bool)

            # 检查移动方向的有效性
            directions = [
                (agv.x, max(0, agv.y - 1)),      # 上
                (agv.x, min(self.height - 1, agv.y + 1)),  # 下
                (max(0, agv.x - 1), agv.y),      # 左
                (min(self.width - 1, agv.x + 1), agv.y)   # 右
            ]

            for i, (x, y) in enumerate(directions):
                if not self._is_valid_move(agv.id, x, y):
                    low_mask[i] = False

            masks.append((high_mask, low_mask))

        return masks

    def get_performance_metrics(self) -> Dict:
        """获取性能评估指标"""
        completed_tasks = sum(1 for task in self.tasks if task.status == TaskStatus.COMPLETED)

        # 1. 任务完成率
        task_completion_rate = completed_tasks / len(self.tasks) if self.tasks else 0

        # 2. AGV载重利用率
        total_load_time = 0
        total_runtime = self.total_timesteps * len(self.agvs)
        for agv in self.agvs:
            if agv.current_load > 0:
                total_load_time += self.total_timesteps  # 简化计算
        load_utilization = total_load_time / total_runtime if total_runtime > 0 else 0

        # 3. 路径长度
        avg_path_length = self.total_path_length / len(self.agvs) if self.agvs else 0

        # 4. 碰撞次数和碰撞率
        collision_rate = self.total_collisions / (self.total_timesteps * len(self.agvs)) if self.total_timesteps > 0 else 0

        return {
            'task_completion_rate': task_completion_rate,
            'agv_load_utilization': load_utilization,
            'total_path_length': self.total_path_length,
            'avg_path_length': avg_path_length,
            'total_collisions': self.total_collisions,
            'collision_rate': collision_rate,
            'total_timesteps': self.total_timesteps,
            'completed_tasks': completed_tasks
        }

    def render(self, save_path: Optional[str] = None):
        """可视化环境状态"""
        if not MATPLOTLIB_AVAILABLE:
            print("Matplotlib not available, cannot render visualization")
            return

        fig, ax = plt.subplots(1, 1, figsize=(13, 5))

        # 绘制网格
        for i in range(self.height + 1):
            ax.axhline(y=i-0.5, color='lightgray', linewidth=0.5)
        for j in range(self.width + 1):
            ax.axvline(x=j-0.5, color='lightgray', linewidth=0.5)

        # 绘制货架
        for y in range(self.height):
            for x in range(self.width):
                if self.grid[y, x] == 1:  # 货架
                    rect = patches.Rectangle((x-0.4, y-0.4), 0.8, 0.8,
                                           linewidth=1, edgecolor='black', facecolor='brown')
                    ax.add_patch(rect)

        # 绘制AGV
        colors = ['red', 'blue', 'green', 'orange']
        for i, agv in enumerate(self.agvs):
            circle = patches.Circle((agv.x, agv.y), 0.3,
                                  color=colors[i % len(colors)], alpha=0.7)
            ax.add_patch(circle)
            # 显示AGV ID和载重
            ax.text(agv.x, agv.y, f'A{agv.id}\n{agv.current_load}',
                   ha='center', va='center', fontsize=8, fontweight='bold')

        # 绘制任务
        for task in self.tasks:
            if task.status != TaskStatus.COMPLETED:
                marker = 'D' if task.status == TaskStatus.UNASSIGNED else 's'
                color = 'yellow' if task.status == TaskStatus.UNASSIGNED else 'lightblue'
                ax.scatter(task.x, task.y, s=100, c=color, marker=marker,
                          edgecolors='black', linewidth=1)
                # 显示任务ID和重量
                ax.text(task.x + 0.3, task.y + 0.3, f'T{task.id}({task.weight})',
                       fontsize=6)

        ax.set_xlim(-0.5, self.width - 0.5)
        ax.set_ylim(-0.5, self.height - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()  # 让(0,0)在左上角
        ax.set_title(f'Multi-AGV Warehouse Environment (Step: {self.total_timesteps})')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')

        # 添加图例
        legend_elements = [
            patches.Patch(color='brown', label='Shelf'),
            patches.Circle((0, 0), 0.1, color='red', label='AGV'),
            patches.Patch(color='yellow', label='Unassigned Task'),
            patches.Patch(color='lightblue', label='Assigned Task')
        ]
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        else:
            plt.show()

        plt.close()

class ActionSpace:
    """层次化动作空间管理器"""

    def __init__(self, num_tasks: int = 16):
        self.num_tasks = num_tasks

        # 高层动作空间定义
        self.high_actions = {
            0: "KEEP_CURRENT",      # 保持当前任务
            **{i+1: f"SELECT_TASK_{i}" for i in range(num_tasks)},  # 选择任务1-16
            17: "WAIT"              # 等待状态
        }

        # 低层动作空间定义
        self.low_actions = {
            0: "MOVE_UP",           # 向上移动
            1: "MOVE_DOWN",         # 向下移动
            2: "MOVE_LEFT",         # 向左移动
            3: "MOVE_RIGHT",        # 向右移动
            4: "STAY"               # 原地等待
        }

        self.high_action_dim = len(self.high_actions)  # 18
        self.low_action_dim = len(self.low_actions)    # 5

    def get_action_description(self, high_action: int, low_action: int) -> str:
        """获取动作描述"""
        high_desc = self.high_actions.get(high_action, f"UNKNOWN_HIGH_{high_action}")
        low_desc = self.low_actions.get(low_action, f"UNKNOWN_LOW_{low_action}")
        return f"({high_desc}, {low_desc})"

    def is_valid_action(self, high_action: int, low_action: int) -> bool:
        """检查动作是否在有效范围内"""
        return (0 <= high_action < self.high_action_dim and
                0 <= low_action < self.low_action_dim)

    def sample_random_action(self, high_mask: np.ndarray, low_mask: np.ndarray) -> Tuple[int, int]:
        """根据掩码采样随机动作"""
        valid_high = np.where(high_mask)[0]
        valid_low = np.where(low_mask)[0]

        if len(valid_high) == 0 or len(valid_low) == 0:
            # 如果没有有效动作，返回默认动作
            return 0, 4  # 保持当前任务，原地等待

        high_action = np.random.choice(valid_high)
        low_action = np.random.choice(valid_low)

        return int(high_action), int(low_action)

class RewardFunction:
    """多维度奖励函数管理器"""

    def __init__(self):
        # 奖励权重参数
        self.weights = {
            # 任务完成奖励权重
            'task_completion': 10.0,
            'task_priority': 0.5,
            'time_efficiency': 1.0,

            # 移动效率权重
            'movement_cost': 0.1,
            'idle_cost': 0.05,
            'distance_guidance': 0.2,

            # 协作奖励权重
            'collision_penalty': 2.0,
            'collision_avoidance': 0.5,

            # 系统效率权重
            'load_balance': 1.0,
            'throughput': 0.1,
            'invalid_action_penalty': 1.0
        }

    def calculate_task_completion_reward(self, task, timestep: int) -> float:
        """计算任务完成奖励"""
        # 基础完成奖励
        base_reward = self.weights['task_completion']

        # 优先级权重
        priority_weight = 1.0 + self.weights['task_priority'] * task.priority

        # 时间效率权重（简化计算）
        optimal_time = 10  # 假设的最优完成时间
        time_weight = max(0.1, 1.0 - (timestep - optimal_time) / 100)
        time_weight *= self.weights['time_efficiency']

        return base_reward * priority_weight * time_weight

    def calculate_movement_reward(self, moved: bool, distance_improved: bool,
                                distance_worsened: bool) -> float:
        """计算移动效率奖励"""
        reward = 0.0

        if moved:
            # 移动成本
            reward -= self.weights['movement_cost']

            # 距离引导奖励
            if distance_improved:
                reward += self.weights['distance_guidance']
            elif distance_worsened:
                reward -= self.weights['distance_guidance'] * 0.75
        else:
            # 等待成本
            reward -= self.weights['idle_cost']

        return reward

    def calculate_collaboration_reward(self, collision_occurred: bool,
                                     collision_avoided: bool) -> float:
        """计算协作奖励"""
        reward = 0.0

        if collision_occurred:
            reward -= self.weights['collision_penalty']
        elif collision_avoided:
            reward += self.weights['collision_avoidance']

        return reward

    def calculate_system_reward(self, load_balance_score: float,
                              throughput: float) -> float:
        """计算系统效率奖励"""
        reward = 0.0

        # 负载均衡奖励
        reward += self.weights['load_balance'] * load_balance_score

        # 吞吐量奖励
        reward += self.weights['throughput'] * throughput

        return reward

    def get_reward_breakdown(self, **kwargs) -> Dict[str, float]:
        """获取奖励分解（用于调试和分析）"""
        breakdown = {}

        if 'task_completion' in kwargs:
            breakdown['task_completion'] = self.calculate_task_completion_reward(
                kwargs['task_completion']['task'],
                kwargs['task_completion']['timestep']
            )

        if 'movement' in kwargs:
            breakdown['movement'] = self.calculate_movement_reward(
                kwargs['movement']['moved'],
                kwargs['movement']['distance_improved'],
                kwargs['movement']['distance_worsened']
            )

        if 'collaboration' in kwargs:
            breakdown['collaboration'] = self.calculate_collaboration_reward(
                kwargs['collaboration']['collision_occurred'],
                kwargs['collaboration']['collision_avoided']
            )

        if 'system' in kwargs:
            breakdown['system'] = self.calculate_system_reward(
                kwargs['system']['load_balance_score'],
                kwargs['system']['throughput']
            )

        breakdown['total'] = sum(breakdown.values())
        return breakdown

# ================================
# 第二部分：特征嵌入与状态空间处理
# ================================

class FeatureEmbedding(nn.Module):
    """特征嵌入网络"""

    def __init__(self, input_dim: int, embed_dim: int = 64):
        super().__init__()
        self.embed_dim = embed_dim
        self.embedding = nn.Sequential(
            nn.Linear(input_dim, embed_dim),
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 输入特征 (..., input_dim)
        Returns:
            embedded features (..., embed_dim)
        """
        return self.embedding(x)

class TaskEmbedding(FeatureEmbedding):
    """任务特征嵌入 (5维 -> 64维)"""

    def __init__(self):
        super().__init__(input_dim=5, embed_dim=64)

class AGVEmbedding(FeatureEmbedding):
    """AGV特征嵌入 (6维 -> 64维)"""

    def __init__(self):
        super().__init__(input_dim=6, embed_dim=64)

class StateProcessor:
    """状态空间处理器"""

    def __init__(self, device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        self.device = device
        if TORCH_AVAILABLE:
            self.task_embedding = TaskEmbedding().to(device)
            self.agv_embedding = AGVEmbedding().to(device)
        else:
            print("PyTorch不可用，StateProcessor将在CPU模式下运行")

    def process_global_state(self, global_state: Dict) -> torch.Tensor:
        """处理全局状态

        Args:
            global_state: 包含agv_features (4,6), task_features (16,5)

        Returns:
            global_state_tensor: (20, 64) - 4个AGV + 16个任务的嵌入特征
        """
        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法处理全局状态")

        # AGV特征嵌入
        agv_features = torch.FloatTensor(global_state['agv_features']).to(self.device)  # (4, 6)
        agv_embedded = self.agv_embedding(agv_features)  # (4, 64)

        # 任务特征嵌入
        task_features = torch.FloatTensor(global_state['task_features']).to(self.device)  # (16, 5)
        task_embedded = self.task_embedding(task_features)  # (16, 64)

        # 拼接全局状态
        global_embedded = torch.cat([agv_embedded, task_embedded], dim=0)  # (20, 64)

        return global_embedded

    def process_local_observations(self, local_observations: List[Dict]) -> List[Dict]:
        """处理局部观察

        Args:
            local_observations: 每个AGV的局部观察列表

        Returns:
            processed_observations: 处理后的局部观察列表
        """
        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法处理局部观察")

        processed = []

        for obs in local_observations:
            # 自身特征嵌入
            self_features = torch.FloatTensor(obs['self_features']).unsqueeze(0).to(self.device)  # (1, 6)
            self_embedded = self.agv_embedding(self_features).squeeze(0)  # (64,)

            # 可见任务嵌入
            visible_tasks = obs['visible_tasks']  # (N, 5)
            if len(visible_tasks) > 0:
                visible_tasks_tensor = torch.FloatTensor(visible_tasks).to(self.device)
                visible_tasks_embedded = self.task_embedding(visible_tasks_tensor)  # (N, 64)
            else:
                visible_tasks_embedded = torch.zeros(0, 64).to(self.device)

            # 附近AGV嵌入
            nearby_agvs = torch.FloatTensor(obs['nearby_agvs']).to(self.device)  # (3, 6)
            nearby_agvs_embedded = self.agv_embedding(nearby_agvs)  # (3, 64)

            processed_obs = {
                'self_embedded': self_embedded,  # (64,)
                'visible_tasks_embedded': visible_tasks_embedded,  # (N, 64)
                'nearby_agvs_embedded': nearby_agvs_embedded  # (3, 64)
            }
            processed.append(processed_obs)

        return processed

# ================================
# 第三部分：双层注意力机制实现
# ================================

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, embed_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert embed_dim % num_heads == 0

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5

    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            query: (batch_size, seq_len_q, embed_dim)
            key: (batch_size, seq_len_k, embed_dim)
            value: (batch_size, seq_len_v, embed_dim)
            mask: (batch_size, seq_len_q, seq_len_k) or None

        Returns:
            output: (batch_size, seq_len_q, embed_dim)
            attention_weights: (batch_size, num_heads, seq_len_q, seq_len_k)
        """
        batch_size, seq_len_q, _ = query.shape
        seq_len_k = key.shape[1]

        # 线性变换
        Q = self.q_proj(query)  # (batch_size, seq_len_q, embed_dim)
        K = self.k_proj(key)    # (batch_size, seq_len_k, embed_dim)
        V = self.v_proj(value)  # (batch_size, seq_len_v, embed_dim)

        # 重塑为多头
        Q = Q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale  # (batch_size, num_heads, seq_len_q, seq_len_k)

        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            scores.masked_fill_(mask == 0, float('-inf'))

        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        output = torch.matmul(attention_weights, V)  # (batch_size, num_heads, seq_len_q, head_dim)

        # 重塑输出
        output = output.transpose(1, 2).contiguous().view(batch_size, seq_len_q, self.embed_dim)
        output = self.out_proj(output)

        return output, attention_weights

class TaskAllocationAttention(nn.Module):
    """第一层：任务分配注意力机制"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.top_k = top_k
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建TaskAllocationAttention")

        # 多头注意力
        self.attention = MultiHeadAttention(embed_dim, num_heads)

        # 约束权重参数
        self.lambda_d = 2.0  # 距离约束权重
        self.lambda_c = 1.0  # 载重约束权重
        self.lambda_p = 0.5  # 优先级约束权重
        self.lambda_t = 0.3  # 时间约束权重

        # 时序一致性参数
        self.lambda_temporal = 0.1
        self.prev_attention_weights = None

        # 层归一化和残差连接
        self.layer_norm = nn.LayerNorm(embed_dim)

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                constraints: Optional[Dict] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            agv_features: AGV特征 (batch_size, num_agvs, embed_dim)
            task_features: 任务特征 (batch_size, num_tasks, embed_dim)
            constraints: 约束信息字典

        Returns:
            output: 注意力输出 (batch_size, num_agvs, embed_dim)
            attention_weights: 注意力权重 (batch_size, num_agvs, num_tasks)
            temporal_loss: 时序一致性损失
        """
        batch_size, num_agvs, _ = agv_features.shape
        num_tasks = task_features.shape[1]

        # 计算原始注意力分数
        output, raw_attention = self.attention(
            query=agv_features,      # (batch_size, num_agvs, embed_dim)
            key=task_features,       # (batch_size, num_tasks, embed_dim)
            value=task_features      # (batch_size, num_tasks, embed_dim)
        )

        # 提取注意力权重 (平均多头)
        attention_weights = raw_attention.mean(dim=1)  # (batch_size, num_agvs, num_tasks)

        # 应用约束增强
        if constraints is not None:
            attention_weights = self._apply_constraints(attention_weights, constraints)

        # Top-K稀疏化
        attention_weights = self._apply_topk_sparsity(attention_weights)

        # 时序一致性约束
        temporal_loss = torch.tensor(0.0, device=self.device)
        if self.prev_attention_weights is not None:
            temporal_loss = self._compute_temporal_consistency_loss(attention_weights)

        # 更新历史注意力权重
        self.prev_attention_weights = attention_weights.detach().clone()

        # 重新计算输出（使用约束后的权重）
        # 扩展权重维度以匹配value
        expanded_weights = attention_weights.unsqueeze(-1)  # (batch_size, num_agvs, num_tasks, 1)
        task_features_expanded = task_features.unsqueeze(1).expand(-1, num_agvs, -1, -1)  # (batch_size, num_agvs, num_tasks, embed_dim)

        # 加权求和
        weighted_output = (expanded_weights * task_features_expanded).sum(dim=2)  # (batch_size, num_agvs, embed_dim)

        # 残差连接和层归一化
        output = self.layer_norm(agv_features + weighted_output)

        return output, attention_weights, temporal_loss

    def _apply_constraints(self, attention_weights: torch.Tensor, constraints: Dict) -> torch.Tensor:
        """应用约束增强"""
        batch_size, num_agvs, num_tasks = attention_weights.shape

        # 距离约束
        if 'distances' in constraints:
            distances = torch.FloatTensor(constraints['distances']).to(self.device)  # (batch_size, num_agvs, num_tasks)
            # 归一化距离到[0,1]范围
            max_dist = distances.max()
            if max_dist > 0:
                distances_norm = distances / max_dist
                distance_constraint = -self.lambda_d * distances_norm
                attention_weights = attention_weights + distance_constraint

        # 载重约束
        if 'capacity_valid' in constraints:
            capacity_mask = torch.BoolTensor(constraints['capacity_valid']).to(self.device)  # (batch_size, num_agvs, num_tasks)
            capacity_constraint = torch.where(capacity_mask,
                                             torch.tensor(self.lambda_c, device=self.device),
                                             torch.tensor(-self.lambda_c, device=self.device))
            attention_weights = attention_weights + capacity_constraint

        # 优先级约束
        if 'priorities' in constraints:
            priorities = torch.FloatTensor(constraints['priorities']).to(self.device)  # (batch_size, num_tasks)
            # 扩展优先级到所有AGV
            priorities_expanded = priorities.unsqueeze(1).expand(-1, num_agvs, -1)  # (batch_size, num_agvs, num_tasks)
            priority_constraint = self.lambda_p * priorities_expanded
            attention_weights = attention_weights + priority_constraint

        # 时间约束
        if 'time_urgency' in constraints:
            time_urgency = torch.FloatTensor(constraints['time_urgency']).to(self.device)  # (batch_size, num_tasks)
            time_urgency_expanded = time_urgency.unsqueeze(1).expand(-1, num_agvs, -1)
            time_constraint = self.lambda_t * time_urgency_expanded
            attention_weights = attention_weights + time_constraint

        # 可用性约束
        if 'task_available' in constraints:
            availability_mask = torch.BoolTensor(constraints['task_available']).to(self.device)  # (batch_size, num_tasks)
            availability_mask = availability_mask.unsqueeze(1).expand(-1, num_agvs, -1)
            attention_weights = attention_weights.masked_fill(~availability_mask, float('-inf'))

        return attention_weights

    def _apply_topk_sparsity(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """应用Top-K稀疏化"""
        batch_size, num_agvs, num_tasks = attention_weights.shape
        k = min(self.top_k, num_tasks)

        # 对每个AGV选择Top-K任务
        topk_values, topk_indices = torch.topk(attention_weights, k, dim=-1)

        # 创建稀疏掩码
        sparse_weights = torch.full_like(attention_weights, float('-inf'))

        # 使用高级索引填充Top-K值
        batch_indices = torch.arange(batch_size, device=self.device).unsqueeze(1).unsqueeze(2).expand(-1, num_agvs, k)
        agv_indices = torch.arange(num_agvs, device=self.device).unsqueeze(0).unsqueeze(2).expand(batch_size, -1, k)

        sparse_weights[batch_indices, agv_indices, topk_indices] = topk_values

        # 重新归一化
        sparse_weights = F.softmax(sparse_weights, dim=-1)

        return sparse_weights

    def _compute_temporal_consistency_loss(self, current_weights: torch.Tensor) -> torch.Tensor:
        """计算时序一致性损失"""
        if self.prev_attention_weights is None:
            return torch.tensor(0.0, device=self.device)

        # 确保维度匹配
        if current_weights.shape != self.prev_attention_weights.shape:
            return torch.tensor(0.0, device=self.device)

        # L2距离
        temporal_loss = F.mse_loss(current_weights, self.prev_attention_weights)
        return self.lambda_temporal * temporal_loss

    def reset_temporal_state(self):
        """重置时序状态（用于新回合开始）"""
        self.prev_attention_weights = None

class CollaborationAttention(nn.Module):
    """第二层：协作感知注意力机制"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建CollaborationAttention")

        # 层次化协作注意力（近、中、远距离）
        self.near_attention = MultiHeadAttention(embed_dim, num_heads)    # 距离 < 3
        self.mid_attention = MultiHeadAttention(embed_dim, num_heads)     # 3 ≤ 距离 < 8
        self.far_attention = MultiHeadAttention(embed_dim, num_heads)     # 距离 ≥ 8

        # 自适应温度机制
        self.temperature_net = nn.Sequential(
            nn.Linear(embed_dim + 1, 32),  # embed_dim + complexity
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()  # 确保温度在(0,1)范围内
        )

        # 协作约束权重
        self.lambda_col = 2.0   # 碰撞风险约束
        self.lambda_path = 1.0  # 路径冲突约束
        self.lambda_bal = 0.5   # 负载均衡约束
        self.lambda_hist = 0.3  # 协作历史约束

        # 层次化权重融合网络
        self.level_weight_net = nn.Sequential(
            nn.Linear(embed_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 3),  # 3个层次：near, mid, far
            nn.Softmax(dim=-1)
        )

        # 相对位置编码
        self.pos_encoding_dim = 6  # [sin(Δx), cos(Δx), sin(Δy), cos(Δy), sin(Δθ), cos(Δθ)]
        self.pos_encoding_net = nn.Linear(self.pos_encoding_dim, embed_dim)

        # 意图表示网络
        self.intent_net = nn.Linear(embed_dim, embed_dim)

        # 协作状态融合网络
        self.collab_fusion_net = nn.Sequential(
            nn.Linear(embed_dim * 3, embed_dim),  # enhanced + intent + pos_avg
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

        # 层归一化
        self.layer_norm = nn.LayerNorm(embed_dim)

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_enhanced_features: torch.Tensor,
                first_layer_attention: torch.Tensor,
                agv_positions: torch.Tensor,
                constraints: Optional[Dict] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            agv_enhanced_features: 第一层增强的AGV特征 (batch_size, num_agvs, embed_dim)
            first_layer_attention: 第一层注意力权重 (batch_size, num_agvs, num_tasks)
            agv_positions: AGV位置信息 (batch_size, num_agvs, 3) [x, y, θ]
            constraints: 协作约束信息字典

        Returns:
            output: 协作感知输出 (batch_size, num_agvs, embed_dim)
            collaboration_weights: 协作注意力权重 (batch_size, num_agvs, num_agvs)
        """
        batch_size, num_agvs, _ = agv_enhanced_features.shape

        # 1. 构建协作状态表示
        collab_states = self._build_collaboration_states(
            agv_enhanced_features, first_layer_attention, agv_positions
        )

        # 2. 计算环境复杂度
        complexity = self._compute_environment_complexity(agv_positions)

        # 3. 层次化协作注意力
        near_output, near_weights = self._compute_hierarchical_attention(
            collab_states, agv_positions, "near", complexity, constraints
        )
        mid_output, mid_weights = self._compute_hierarchical_attention(
            collab_states, agv_positions, "mid", complexity, constraints
        )
        far_output, far_weights = self._compute_hierarchical_attention(
            collab_states, agv_positions, "far", complexity, constraints
        )

        # 4. 自适应权重融合
        fusion_weights = self._compute_fusion_weights(collab_states, complexity)

        # 5. 融合不同层次的输出
        fused_output = (fusion_weights[:, :, 0:1] * near_output +
                       fusion_weights[:, :, 1:2] * mid_output +
                       fusion_weights[:, :, 2:3] * far_output)

        # 6. 融合协作权重
        # near_weights, mid_weights, far_weights 形状: (batch_size, num_agvs, num_agvs, 1)
        # fusion_weights 形状: (batch_size, num_agvs, 3)
        near_weights_2d = near_weights.squeeze(-1)  # (batch_size, num_agvs, num_agvs)
        mid_weights_2d = mid_weights.squeeze(-1)    # (batch_size, num_agvs, num_agvs)
        far_weights_2d = far_weights.squeeze(-1)    # (batch_size, num_agvs, num_agvs)

        collaboration_weights = (fusion_weights[:, :, 0:1] * near_weights_2d +
                               fusion_weights[:, :, 1:2] * mid_weights_2d +
                               fusion_weights[:, :, 2:3] * far_weights_2d)

        # 7. 残差连接和层归一化
        output = self.layer_norm(agv_enhanced_features + fused_output)

        return output, collaboration_weights.squeeze(-1)

    def _build_collaboration_states(self, agv_enhanced_features: torch.Tensor,
                                   first_layer_attention: torch.Tensor,
                                   agv_positions: torch.Tensor) -> torch.Tensor:
        """构建协作状态表示"""
        batch_size, num_agvs, embed_dim = agv_enhanced_features.shape

        # 1. 基础状态融合（已经是增强的特征）
        enhanced_states = agv_enhanced_features

        # 2. 意图表示（基于第一层注意力权重推断AGV意图）
        # 使用注意力权重的加权平均作为意图向量
        task_features = torch.randn(batch_size, 16, embed_dim, device=self.device)  # 简化：使用随机任务特征
        intent_vectors = torch.bmm(first_layer_attention, task_features)  # (batch_size, num_agvs, embed_dim)
        intent_enhanced = self.intent_net(intent_vectors)

        # 3. 相对位置编码
        pos_encodings = []
        for i in range(num_agvs):
            agv_pos_encodings = []
            for j in range(num_agvs):
                if i != j:
                    # 计算相对位置
                    delta_x = agv_positions[:, i, 0] - agv_positions[:, j, 0]
                    delta_y = agv_positions[:, i, 1] - agv_positions[:, j, 1]
                    delta_theta = agv_positions[:, i, 2] - agv_positions[:, j, 2]

                    # 位置编码
                    sigma = 10.0  # 位置编码的尺度参数
                    pos_encoding = torch.stack([
                        torch.sin(delta_x / sigma),
                        torch.cos(delta_x / sigma),
                        torch.sin(delta_y / sigma),
                        torch.cos(delta_y / sigma),
                        torch.sin(delta_theta),
                        torch.cos(delta_theta)
                    ], dim=-1)  # (batch_size, 6)

                    agv_pos_encodings.append(pos_encoding)
                else:
                    # 自己与自己的相对位置为零
                    agv_pos_encodings.append(torch.zeros(batch_size, 6, device=self.device))

            pos_encodings.append(torch.stack(agv_pos_encodings, dim=1))  # (batch_size, num_agvs, 6)

        pos_encodings = torch.stack(pos_encodings, dim=1)  # (batch_size, num_agvs, num_agvs, 6)

        # 计算平均相对位置编码
        pos_avg = pos_encodings.mean(dim=2)  # (batch_size, num_agvs, 6)
        pos_encoded = self.pos_encoding_net(pos_avg)  # (batch_size, num_agvs, embed_dim)

        # 4. 协作状态融合
        collab_input = torch.cat([enhanced_states, intent_enhanced, pos_encoded], dim=-1)
        collab_states = self.collab_fusion_net(collab_input)

        return collab_states

    def _compute_environment_complexity(self, agv_positions: torch.Tensor) -> torch.Tensor:
        """计算环境复杂度"""
        batch_size, num_agvs, _ = agv_positions.shape

        # 简化的复杂度计算：基于AGV密度
        # 计算所有AGV对之间的平均距离
        distances = []
        for i in range(num_agvs):
            for j in range(i + 1, num_agvs):
                dist = torch.sqrt(
                    (agv_positions[:, i, 0] - agv_positions[:, j, 0]) ** 2 +
                    (agv_positions[:, i, 1] - agv_positions[:, j, 1]) ** 2
                )
                distances.append(dist)

        if distances:
            avg_distance = torch.stack(distances, dim=-1).mean(dim=-1)  # (batch_size,)
            # 距离越小，复杂度越高
            complexity = 1.0 / (1.0 + avg_distance)
        else:
            complexity = torch.zeros(batch_size, device=self.device)

        return complexity

    def _compute_hierarchical_attention(self, collab_states: torch.Tensor,
                                      agv_positions: torch.Tensor,
                                      level: str, complexity: torch.Tensor,
                                      constraints: Optional[Dict] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算层次化协作注意力"""
        batch_size, num_agvs, embed_dim = collab_states.shape

        # 根据距离级别选择注意力机制
        if level == "near":
            attention_module = self.near_attention
            distance_threshold = (0, 3)
        elif level == "mid":
            attention_module = self.mid_attention
            distance_threshold = (3, 8)
        else:  # far
            attention_module = self.far_attention
            distance_threshold = (8, float('inf'))

        # 创建距离掩码
        distance_mask = self._create_distance_mask(agv_positions, distance_threshold)

        # 计算注意力
        output, raw_attention = attention_module(
            query=collab_states,
            key=collab_states,
            value=collab_states,
            mask=distance_mask
        )

        # 提取注意力权重（平均多头）
        attention_weights = raw_attention.mean(dim=1)  # (batch_size, num_agvs, num_agvs)

        # 应用协作约束
        if constraints is not None:
            attention_weights = self._apply_collaboration_constraints(
                attention_weights, agv_positions, constraints
            )

        # 应用自适应温度
        temperature = self._compute_adaptive_temperature(collab_states, complexity)
        attention_weights = attention_weights / temperature.unsqueeze(-1)
        attention_weights = F.softmax(attention_weights, dim=-1)

        return output, attention_weights.unsqueeze(-1)  # 添加维度以便后续融合

    def _create_distance_mask(self, agv_positions: torch.Tensor,
                             distance_threshold: Tuple[float, float]) -> torch.Tensor:
        """创建基于距离的注意力掩码"""
        batch_size, num_agvs, _ = agv_positions.shape
        min_dist, max_dist = distance_threshold

        # 计算所有AGV对之间的距离
        distances = torch.zeros(batch_size, num_agvs, num_agvs, device=self.device)

        for i in range(num_agvs):
            for j in range(num_agvs):
                if i != j:
                    dist = torch.sqrt(
                        (agv_positions[:, i, 0] - agv_positions[:, j, 0]) ** 2 +
                        (agv_positions[:, i, 1] - agv_positions[:, j, 1]) ** 2
                    )
                    distances[:, i, j] = dist

        # 创建掩码：在距离范围内的为True
        if max_dist == float('inf'):
            mask = distances >= min_dist
        else:
            mask = (distances >= min_dist) & (distances < max_dist)

        # 对角线设为False（AGV不与自己交互）
        for i in range(num_agvs):
            mask[:, i, i] = False

        return mask

    def _apply_collaboration_constraints(self, attention_weights: torch.Tensor,
                                       agv_positions: torch.Tensor,
                                       constraints: Dict) -> torch.Tensor:
        """应用协作约束"""
        batch_size, num_agvs, _ = attention_weights.shape

        # 碰撞风险约束
        if 'collision_risk' in constraints:
            collision_risk = torch.FloatTensor(constraints['collision_risk']).to(self.device)
            collision_constraint = -self.lambda_col * collision_risk
            attention_weights = attention_weights + collision_constraint

        # 路径冲突约束
        if 'path_conflict' in constraints:
            path_conflict = torch.FloatTensor(constraints['path_conflict']).to(self.device)
            path_constraint = -self.lambda_path * path_conflict
            attention_weights = attention_weights + path_constraint

        # 负载均衡约束
        if 'load_imbalance' in constraints:
            load_imbalance = torch.FloatTensor(constraints['load_imbalance']).to(self.device)
            balance_constraint = self.lambda_bal * (1.0 - load_imbalance)
            attention_weights = attention_weights + balance_constraint

        # 协作历史约束
        if 'collaboration_history' in constraints:
            collab_history = torch.FloatTensor(constraints['collaboration_history']).to(self.device)
            history_constraint = self.lambda_hist * collab_history
            attention_weights = attention_weights + history_constraint

        return attention_weights

    def _compute_adaptive_temperature(self, collab_states: torch.Tensor,
                                    complexity: torch.Tensor) -> torch.Tensor:
        """计算自适应温度参数"""
        batch_size, num_agvs, embed_dim = collab_states.shape

        # 为每个AGV计算温度
        temperatures = []
        for i in range(num_agvs):
            # 将协作状态和复杂度拼接
            temp_input = torch.cat([
                collab_states[:, i, :],  # (batch_size, embed_dim)
                complexity.unsqueeze(-1)  # (batch_size, 1)
            ], dim=-1)

            temp = self.temperature_net(temp_input)  # (batch_size, 1)
            temperatures.append(temp)

        temperatures = torch.stack(temperatures, dim=1)  # (batch_size, num_agvs, 1)

        # 确保温度在合理范围内 [0.1, 1.0]
        temperatures = 0.1 + 0.9 * temperatures

        return temperatures

    def _compute_fusion_weights(self, collab_states: torch.Tensor,
                              complexity: torch.Tensor) -> torch.Tensor:
        """计算层次化权重融合"""
        batch_size, num_agvs, embed_dim = collab_states.shape

        # 为每个AGV计算融合权重
        fusion_weights = []
        for i in range(num_agvs):
            weights = self.level_weight_net(collab_states[:, i, :])  # (batch_size, 3)
            fusion_weights.append(weights)

        fusion_weights = torch.stack(fusion_weights, dim=1)  # (batch_size, num_agvs, 3)

        return fusion_weights

class DualLayerAttentionFusion(nn.Module):
    """双层注意力融合机制"""

    def __init__(self, embed_dim: int = 64,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建DualLayerAttentionFusion")

        # 门控融合机制
        self.gate_network = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),  # 两层注意力输出拼接
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim),
            nn.Sigmoid()  # 门控值在[0,1]范围内
        )

        # 特征变换网络
        self.task_transform = nn.Linear(embed_dim, embed_dim)
        self.collab_transform = nn.Linear(embed_dim, embed_dim)

        # 融合后的特征增强网络
        self.fusion_enhance = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim * 2, embed_dim)
        )

        # 层归一化
        self.layer_norm1 = nn.LayerNorm(embed_dim)
        self.layer_norm2 = nn.LayerNorm(embed_dim)

        # 最终输出投影
        self.output_projection = nn.Linear(embed_dim, embed_dim)

        # 移动到指定设备
        self.to(device)

    def forward(self, task_attention_output: torch.Tensor,
                collaboration_output: torch.Tensor,
                original_agv_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            task_attention_output: 第一层任务分配注意力输出 (batch_size, num_agvs, embed_dim)
            collaboration_output: 第二层协作感知注意力输出 (batch_size, num_agvs, embed_dim)
            original_agv_features: 原始AGV特征 (batch_size, num_agvs, embed_dim)

        Returns:
            fused_output: 融合后的最终输出 (batch_size, num_agvs, embed_dim)
            gate_values: 门控值 (batch_size, num_agvs, embed_dim)
        """
        batch_size, num_agvs, embed_dim = task_attention_output.shape

        # 1. 特征变换
        task_transformed = self.task_transform(task_attention_output)
        collab_transformed = self.collab_transform(collaboration_output)

        # 2. 门控融合策略
        gate_values = self._compute_gate_values(task_transformed, collab_transformed)

        # 3. 加权融合
        weighted_fusion = gate_values * task_transformed + (1 - gate_values) * collab_transformed

        # 4. 第一次残差连接和层归一化
        residual_output1 = self.layer_norm1(original_agv_features + weighted_fusion)

        # 5. 特征增强
        enhanced_features = self.fusion_enhance(residual_output1)

        # 6. 第二次残差连接和层归一化
        residual_output2 = self.layer_norm2(residual_output1 + enhanced_features)

        # 7. 最终输出投影
        fused_output = self.output_projection(residual_output2)

        return fused_output, gate_values

    def _compute_gate_values(self, task_features: torch.Tensor,
                           collab_features: torch.Tensor) -> torch.Tensor:
        """计算门控值"""
        # 拼接两层特征
        concatenated = torch.cat([task_features, collab_features], dim=-1)  # (batch_size, num_agvs, embed_dim*2)

        # 通过门控网络计算门控值
        gate_values = self.gate_network(concatenated)  # (batch_size, num_agvs, embed_dim)

        return gate_values

    def get_attention_statistics(self, gate_values: torch.Tensor) -> Dict[str, float]:
        """获取注意力融合统计信息"""
        gate_np = gate_values.detach().cpu().numpy()

        stats = {
            'mean_gate_value': float(gate_np.mean()),
            'std_gate_value': float(gate_np.std()),
            'min_gate_value': float(gate_np.min()),
            'max_gate_value': float(gate_np.max()),
            'task_attention_dominance': float((gate_np > 0.5).mean()),  # 任务注意力占主导的比例
            'collaboration_dominance': float((gate_np <= 0.5).mean()),  # 协作注意力占主导的比例
        }

        return stats

class IntegratedDualAttention(nn.Module):
    """集成的双层注意力系统"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建IntegratedDualAttention")

        # 第一层：任务分配注意力
        self.task_attention = TaskAllocationAttention(embed_dim, num_heads, top_k, device)

        # 第二层：协作感知注意力
        self.collaboration_attention = CollaborationAttention(embed_dim, num_heads, device)

        # 双层注意力融合
        self.attention_fusion = DualLayerAttentionFusion(embed_dim, device)

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_constraints: Optional[Dict] = None,
                collaboration_constraints: Optional[Dict] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            agv_features: AGV特征 (batch_size, num_agvs, embed_dim)
            task_features: 任务特征 (batch_size, num_tasks, embed_dim)
            agv_positions: AGV位置 (batch_size, num_agvs, 3)
            task_constraints: 任务分配约束
            collaboration_constraints: 协作约束

        Returns:
            Dict包含所有输出和中间结果
        """
        # 第一层：任务分配注意力
        task_output, task_attention_weights, temporal_loss = self.task_attention(
            agv_features, task_features, task_constraints
        )

        # 第二层：协作感知注意力
        collab_output, collab_attention_weights = self.collaboration_attention(
            task_output, task_attention_weights, agv_positions, collaboration_constraints
        )

        # 双层注意力融合
        final_output, gate_values = self.attention_fusion(
            task_output, collab_output, agv_features
        )

        # 返回完整的结果字典
        results = {
            'final_output': final_output,
            'task_attention_output': task_output,
            'collaboration_output': collab_output,
            'task_attention_weights': task_attention_weights,
            'collaboration_weights': collab_attention_weights,
            'gate_values': gate_values,
            'temporal_loss': temporal_loss
        }

        return results

    def reset_temporal_states(self):
        """重置所有时序状态"""
        self.task_attention.reset_temporal_state()

    def get_comprehensive_statistics(self, results: Dict[str, torch.Tensor]) -> Dict[str, any]:
        """获取综合统计信息"""
        stats = {}

        # 门控融合统计
        gate_stats = self.attention_fusion.get_attention_statistics(results['gate_values'])
        stats['fusion'] = gate_stats

        # 任务注意力统计
        task_weights = results['task_attention_weights'].detach().cpu().numpy()
        stats['task_attention'] = {
            'mean_attention': float(task_weights.mean()),
            'max_attention': float(task_weights.max()),
            'attention_entropy': float(-np.sum(task_weights * np.log(task_weights + 1e-8), axis=-1).mean())
        }

        # 协作注意力统计
        if len(results['collaboration_weights'].shape) == 4:
            collab_weights = results['collaboration_weights'].detach().cpu().numpy().mean(axis=-1)
        else:
            collab_weights = results['collaboration_weights'].detach().cpu().numpy()

        stats['collaboration'] = {
            'mean_collaboration': float(collab_weights.mean()),
            'max_collaboration': float(collab_weights.max()),
            'collaboration_variance': float(collab_weights.var())
        }

        # 时序损失
        stats['temporal_loss'] = float(results['temporal_loss'].item())

        return stats

# ================================
# 第四部分：MAPPO框架集成与策略网络
# ================================

class AttentionEnhancedPolicyNetwork(nn.Module):
    """注意力增强策略网络"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8,
                 high_level_actions: int = 16, low_level_actions: int = 5,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.top_k = top_k
        self.high_level_actions = high_level_actions  # 任务选择动作数
        self.low_level_actions = low_level_actions    # 运动控制动作数
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建AttentionEnhancedPolicyNetwork")

        # 输入层和特征提取
        self.input_projection = nn.Linear(embed_dim, embed_dim)
        self.feature_extractor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.LayerNorm(embed_dim * 2),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

        # 双层注意力机制
        self.dual_attention = IntegratedDualAttention(embed_dim, num_heads, top_k, device)

        # 注意力特征增强
        self.attention_feature_enhancer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim)
        )

        # 层次化动作生成网络
        # 高层：任务选择网络
        self.high_level_policy = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, high_level_actions)
        )

        # 低层：运动控制网络
        self.low_level_policy = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, low_level_actions)
        )

        # 动作掩码生成网络
        self.mask_generator = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, high_level_actions + low_level_actions),
            nn.Sigmoid()  # 输出[0,1]范围的掩码权重
        )

        # 策略融合网络
        self.policy_fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),  # 高层+低层特征融合
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_constraints: Optional[Dict] = None,
                collaboration_constraints: Optional[Dict] = None,
                action_masks: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            agv_features: AGV特征 (batch_size, num_agvs, embed_dim)
            task_features: 任务特征 (batch_size, num_tasks, embed_dim)
            agv_positions: AGV位置 (batch_size, num_agvs, 3)
            task_constraints: 任务分配约束
            collaboration_constraints: 协作约束
            action_masks: 外部动作掩码 (batch_size, num_agvs, total_actions)

        Returns:
            Dict包含策略输出和中间结果
        """
        batch_size, num_agvs, _ = agv_features.shape

        # 1. 输入投影和特征提取
        projected_features = self.input_projection(agv_features)
        extracted_features = self.feature_extractor(projected_features)

        # 2. 双层注意力机制
        attention_results = self.dual_attention(
            extracted_features, task_features, agv_positions,
            task_constraints, collaboration_constraints
        )

        # 3. 注意力特征增强
        attention_enhanced = self.attention_feature_enhancer(attention_results['final_output'])

        # 4. 层次化动作生成
        high_level_logits = self.high_level_policy(attention_enhanced)  # (batch_size, num_agvs, high_actions)
        low_level_logits = self.low_level_policy(attention_enhanced)    # (batch_size, num_agvs, low_actions)

        # 5. 智能动作掩码生成
        mask_weights = self.mask_generator(attention_enhanced)  # (batch_size, num_agvs, total_actions)

        # 分离高层和低层掩码
        high_level_masks = mask_weights[:, :, :self.high_level_actions]
        low_level_masks = mask_weights[:, :, self.high_level_actions:]

        # 6. 应用动作掩码
        if action_masks is not None:
            # 结合外部掩码和智能掩码
            external_high_masks = action_masks[:, :, :self.high_level_actions]
            external_low_masks = action_masks[:, :, self.high_level_actions:]

            high_level_masks = high_level_masks * external_high_masks
            low_level_masks = low_level_masks * external_low_masks

        # 应用掩码到logits（使用更稳定的方法）
        # 将无效动作的logits设为很小的值而不是-inf
        masked_high_logits = high_level_logits.clone()
        masked_low_logits = low_level_logits.clone()

        # 对于掩码值很小的动作，将logits设为很小的值
        masked_high_logits = torch.where(high_level_masks > 1e-6,
                                       masked_high_logits,
                                       torch.full_like(masked_high_logits, -1e6))
        masked_low_logits = torch.where(low_level_masks > 1e-6,
                                      masked_low_logits,
                                      torch.full_like(masked_low_logits, -1e6))

        # 7. 计算动作概率分布
        high_level_probs = F.softmax(masked_high_logits, dim=-1)
        low_level_probs = F.softmax(masked_low_logits, dim=-1)

        # 数值稳定性检查
        high_level_probs = torch.clamp(high_level_probs, min=1e-8, max=1.0)
        low_level_probs = torch.clamp(low_level_probs, min=1e-8, max=1.0)

        # 重新归一化
        high_level_probs = high_level_probs / high_level_probs.sum(dim=-1, keepdim=True)
        low_level_probs = low_level_probs / low_level_probs.sum(dim=-1, keepdim=True)

        # 8. 策略融合（用于价值估计）
        high_level_features = torch.mean(high_level_probs.unsqueeze(-1) * attention_enhanced.unsqueeze(-2), dim=-2)
        low_level_features = torch.mean(low_level_probs.unsqueeze(-1) * attention_enhanced.unsqueeze(-2), dim=-2)

        fused_features = self.policy_fusion(torch.cat([high_level_features, low_level_features], dim=-1))

        # 返回完整结果
        results = {
            'high_level_logits': high_level_logits,
            'low_level_logits': low_level_logits,
            'high_level_probs': high_level_probs,
            'low_level_probs': low_level_probs,
            'high_level_masks': high_level_masks,
            'low_level_masks': low_level_masks,
            'fused_features': fused_features,
            'attention_enhanced': attention_enhanced,
            'attention_results': attention_results
        }

        return results

    def sample_actions(self, policy_output: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """从策略分布中采样动作"""
        high_level_dist = torch.distributions.Categorical(policy_output['high_level_probs'])
        low_level_dist = torch.distributions.Categorical(policy_output['low_level_probs'])

        high_level_actions = high_level_dist.sample()  # (batch_size, num_agvs)
        low_level_actions = low_level_dist.sample()    # (batch_size, num_agvs)

        return high_level_actions, low_level_actions

    def evaluate_actions(self, policy_output: Dict[str, torch.Tensor],
                        high_level_actions: torch.Tensor,
                        low_level_actions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """评估给定动作的概率和熵"""
        high_level_dist = torch.distributions.Categorical(policy_output['high_level_probs'])
        low_level_dist = torch.distributions.Categorical(policy_output['low_level_probs'])

        high_level_log_probs = high_level_dist.log_prob(high_level_actions)
        low_level_log_probs = low_level_dist.log_prob(low_level_actions)

        high_level_entropy = high_level_dist.entropy()
        low_level_entropy = low_level_dist.entropy()

        return {
            'high_level_log_probs': high_level_log_probs,
            'low_level_log_probs': low_level_log_probs,
            'high_level_entropy': high_level_entropy,
            'low_level_entropy': low_level_entropy,
            'total_log_probs': high_level_log_probs + low_level_log_probs,
            'total_entropy': high_level_entropy + low_level_entropy
        }

    def reset_temporal_states(self):
        """重置时序状态"""
        self.dual_attention.reset_temporal_states()

    def get_action_masks(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                        env_state: Dict) -> torch.Tensor:
        """生成智能动作掩码"""
        batch_size, num_agvs, _ = agv_features.shape

        # 基于环境状态生成掩码
        action_masks = torch.ones(batch_size, num_agvs,
                                 self.high_level_actions + self.low_level_actions,
                                 device=self.device)

        # 任务可用性掩码
        if 'task_available' in env_state:
            task_available = torch.BoolTensor(env_state['task_available']).to(self.device)  # (16,)
            # 扩展到所有AGV: (batch_size, num_agvs, num_tasks)
            task_available_expanded = task_available.unsqueeze(0).unsqueeze(0).expand(batch_size, num_agvs, -1)
            # 不可用任务的掩码设为0
            action_masks[:, :, :self.high_level_actions] *= task_available_expanded.float()

        # 碰撞避免掩码
        if 'collision_risk' in env_state:
            collision_risk = torch.FloatTensor(env_state['collision_risk']).to(self.device)
            # 高碰撞风险的运动动作掩码降低
            risk_threshold = 0.7
            safe_movement = (collision_risk < risk_threshold).float()
            action_masks[:, :, self.high_level_actions:] *= safe_movement.mean(dim=-1, keepdim=True)

        return action_masks

class AttentionEnhancedValueNetwork(nn.Module):
    """注意力增强价值网络"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.top_k = top_k
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建AttentionEnhancedValueNetwork")

        # 全局状态编码器
        self.global_state_encoder = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.LayerNorm(embed_dim * 2),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

        # 双层注意力机制（共享或独立）
        self.dual_attention = IntegratedDualAttention(embed_dim, num_heads, top_k, device)

        # 全局注意力机制
        self.global_attention = MultiHeadAttention(embed_dim, num_heads)

        # 个体价值估计网络
        self.individual_value_net = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 1)  # 输出单个价值
        )

        # 全局价值函数网络
        self.global_value_net = nn.Sequential(
            nn.Linear(embed_dim * 4, embed_dim * 2),  # 4个AGV的特征拼接
            nn.ReLU(),
            nn.LayerNorm(embed_dim * 2),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, 1)  # 输出全局价值
        )

        # 价值融合网络
        self.value_fusion_net = nn.Sequential(
            nn.Linear(embed_dim + 1, embed_dim // 2),  # 个体特征 + 全局价值
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 1)
        )

        # 注意力一致性网络
        self.attention_consistency_net = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),  # 策略特征 + 价值特征
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 1)  # 一致性分数
        )

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_constraints: Optional[Dict] = None,
                collaboration_constraints: Optional[Dict] = None,
                policy_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            agv_features: AGV特征 (batch_size, num_agvs, embed_dim)
            task_features: 任务特征 (batch_size, num_tasks, embed_dim)
            agv_positions: AGV位置 (batch_size, num_agvs, 3)
            task_constraints: 任务分配约束
            collaboration_constraints: 协作约束
            policy_features: 策略网络的融合特征 (batch_size, num_agvs, embed_dim)

        Returns:
            Dict包含价值估计和中间结果
        """
        batch_size, num_agvs, _ = agv_features.shape

        # 1. 全局状态编码
        global_encoded = self.global_state_encoder(agv_features)

        # 2. 双层注意力机制（价值网络专用）
        attention_results = self.dual_attention(
            global_encoded, task_features, agv_positions,
            task_constraints, collaboration_constraints
        )

        value_enhanced_features = attention_results['final_output']

        # 3. 全局注意力机制
        global_attention_output, global_attention_weights = self.global_attention(
            query=value_enhanced_features,
            key=value_enhanced_features,
            value=value_enhanced_features
        )

        # 平均多头注意力权重
        global_attention_weights = global_attention_weights.mean(dim=1)  # (batch_size, num_agvs, num_agvs)

        # 4. 个体价值估计
        individual_values = self.individual_value_net(global_attention_output)  # (batch_size, num_agvs, 1)
        individual_values = individual_values.squeeze(-1)  # (batch_size, num_agvs)

        # 5. 全局价值函数
        # 将所有AGV特征拼接
        global_features = global_attention_output.view(batch_size, -1)  # (batch_size, num_agvs * embed_dim)
        global_value = self.global_value_net(global_features)  # (batch_size, 1)
        global_value = global_value.squeeze(-1)  # (batch_size,)

        # 6. 价值融合
        # 将全局价值广播到每个AGV
        global_value_expanded = global_value.unsqueeze(1).expand(-1, num_agvs)  # (batch_size, num_agvs)

        # 融合个体特征和全局价值
        fusion_input = torch.cat([
            global_attention_output,  # (batch_size, num_agvs, embed_dim)
            global_value_expanded.unsqueeze(-1)  # (batch_size, num_agvs, 1)
        ], dim=-1)

        fused_values = self.value_fusion_net(fusion_input)  # (batch_size, num_agvs, 1)
        fused_values = fused_values.squeeze(-1)  # (batch_size, num_agvs)

        # 7. 注意力一致性计算
        consistency_scores = torch.zeros(batch_size, num_agvs, device=self.device)
        if policy_features is not None:
            # 计算策略特征和价值特征的一致性
            consistency_input = torch.cat([
                policy_features,  # (batch_size, num_agvs, embed_dim)
                global_attention_output  # (batch_size, num_agvs, embed_dim)
            ], dim=-1)

            consistency_scores = self.attention_consistency_net(consistency_input)  # (batch_size, num_agvs, 1)
            consistency_scores = consistency_scores.squeeze(-1)  # (batch_size, num_agvs)

        # 返回完整结果
        results = {
            'individual_values': individual_values,
            'global_value': global_value,
            'fused_values': fused_values,
            'consistency_scores': consistency_scores,
            'global_attention_weights': global_attention_weights,
            'value_enhanced_features': value_enhanced_features,
            'attention_results': attention_results
        }

        return results

    def reset_temporal_states(self):
        """重置时序状态"""
        self.dual_attention.reset_temporal_states()

    def compute_value_loss(self, predicted_values: torch.Tensor,
                          target_values: torch.Tensor,
                          value_type: str = 'individual') -> torch.Tensor:
        """计算价值损失"""
        if value_type == 'individual':
            # 个体价值损失（MSE）
            loss = F.mse_loss(predicted_values, target_values)
        elif value_type == 'global':
            # 全局价值损失（MSE）
            loss = F.mse_loss(predicted_values, target_values)
        elif value_type == 'fused':
            # 融合价值损失（Huber损失，更稳定）
            loss = F.smooth_l1_loss(predicted_values, target_values)
        else:
            raise ValueError(f"未知的价值类型: {value_type}")

        return loss

    def compute_attention_consistency_loss(self, consistency_scores: torch.Tensor,
                                         target_consistency: Optional[torch.Tensor] = None) -> torch.Tensor:
        """计算注意力一致性损失"""
        if target_consistency is None:
            # 如果没有目标一致性，鼓励高一致性（接近1）
            target_consistency = torch.ones_like(consistency_scores)

        # 使用BCE损失
        consistency_loss = F.binary_cross_entropy_with_logits(
            consistency_scores, target_consistency
        )

        return consistency_loss

    def get_value_statistics(self, value_output: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """获取价值网络统计信息"""
        stats = {}

        # 个体价值统计
        individual_values = value_output['individual_values'].detach().cpu().numpy()
        stats['individual_value_mean'] = float(individual_values.mean())
        stats['individual_value_std'] = float(individual_values.std())
        stats['individual_value_min'] = float(individual_values.min())
        stats['individual_value_max'] = float(individual_values.max())

        # 全局价值统计
        global_value = value_output['global_value'].detach().cpu().numpy()
        stats['global_value_mean'] = float(global_value.mean())
        stats['global_value_std'] = float(global_value.std())

        # 融合价值统计
        fused_values = value_output['fused_values'].detach().cpu().numpy()
        stats['fused_value_mean'] = float(fused_values.mean())
        stats['fused_value_std'] = float(fused_values.std())

        # 一致性分数统计
        consistency_scores = value_output['consistency_scores'].detach().cpu().numpy()
        stats['consistency_mean'] = float(consistency_scores.mean())
        stats['consistency_std'] = float(consistency_scores.std())

        # 全局注意力权重统计
        global_attention = value_output['global_attention_weights'].detach().cpu().numpy()
        stats['global_attention_mean'] = float(global_attention.mean())
        stats['global_attention_entropy'] = float(-np.sum(global_attention * np.log(global_attention + 1e-8), axis=-1).mean())

        return stats

class MAPPOWithDualAttention(nn.Module):
    """集成双层注意力的MAPPO系统"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8,
                 high_level_actions: int = 16, low_level_actions: int = 5,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.embed_dim = embed_dim
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建MAPPOWithDualAttention")

        # 策略网络
        self.policy_network = AttentionEnhancedPolicyNetwork(
            embed_dim, num_heads, top_k, high_level_actions, low_level_actions, device
        )

        # 价值网络
        self.value_network = AttentionEnhancedValueNetwork(
            embed_dim, num_heads, top_k, device
        )

        # 移动到指定设备
        self.to(device)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_constraints: Optional[Dict] = None,
                collaboration_constraints: Optional[Dict] = None,
                action_masks: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        完整的MAPPO前向传播

        Returns:
            Dict包含策略和价值的所有输出
        """
        # 策略网络前向传播
        policy_output = self.policy_network(
            agv_features, task_features, agv_positions,
            task_constraints, collaboration_constraints, action_masks
        )

        # 价值网络前向传播（使用策略特征）
        value_output = self.value_network(
            agv_features, task_features, agv_positions,
            task_constraints, collaboration_constraints,
            policy_output['fused_features']
        )

        # 合并输出
        combined_output = {
            **policy_output,
            **value_output,
            'policy_output': policy_output,
            'value_output': value_output
        }

        return combined_output

    def reset_temporal_states(self):
        """重置所有时序状态"""
        self.policy_network.reset_temporal_states()
        self.value_network.reset_temporal_states()

# ================================
# 第五部分：训练目标函数设计
# ================================

class MAPPOLossFunction(nn.Module):
    """MAPPO训练目标函数"""

    def __init__(self, clip_ratio: float = 0.2, entropy_coef: float = 0.01,
                 value_coef: float = 0.5, attention_reg_coef: float = 0.1,
                 temporal_consistency_coef: float = 0.05, consistency_coef: float = 0.1,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        super().__init__()
        self.clip_ratio = clip_ratio
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.attention_reg_coef = attention_reg_coef
        self.temporal_consistency_coef = temporal_consistency_coef
        self.consistency_coef = consistency_coef
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建MAPPOLossFunction")

        # 移动到指定设备
        self.to(device)

    def compute_policy_loss(self, current_output: Dict[str, torch.Tensor],
                           old_log_probs: torch.Tensor, advantages: torch.Tensor,
                           old_attention_weights: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        计算策略损失函数

        Args:
            current_output: 当前策略网络输出
            old_log_probs: 旧策略的log概率 (batch_size, num_agvs)
            advantages: 优势函数 (batch_size, num_agvs)
            old_attention_weights: 旧的注意力权重 (batch_size, num_agvs, num_tasks)

        Returns:
            Dict包含各种损失组件
        """
        # 1. 基础PPO损失
        current_log_probs = current_output['attention_results']['task_attention_output']
        if 'total_log_probs' in current_output:
            current_log_probs = current_output['total_log_probs']
        else:
            # 如果没有total_log_probs，使用高层和低层log概率的和
            high_log_probs = torch.distributions.Categorical(current_output['high_level_probs']).log_prob(
                torch.argmax(current_output['high_level_probs'], dim=-1)
            )
            low_log_probs = torch.distributions.Categorical(current_output['low_level_probs']).log_prob(
                torch.argmax(current_output['low_level_probs'], dim=-1)
            )
            current_log_probs = high_log_probs + low_log_probs

        # 计算重要性采样比率
        ratio = torch.exp(current_log_probs - old_log_probs)

        # PPO裁剪损失
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * advantages
        ppo_loss = -torch.min(surr1, surr2).mean()

        # 2. 熵损失（鼓励探索）
        high_entropy = torch.distributions.Categorical(current_output['high_level_probs']).entropy()
        low_entropy = torch.distributions.Categorical(current_output['low_level_probs']).entropy()
        total_entropy = high_entropy + low_entropy
        entropy_loss = -self.entropy_coef * total_entropy.mean()

        # 3. 注意力正则化损失
        attention_reg_loss = torch.tensor(0.0, device=self.device)
        if 'attention_results' in current_output:
            attention_results = current_output['attention_results']

            # 任务注意力正则化（鼓励稀疏性）
            task_attention = attention_results['task_attention_weights']
            attention_entropy = -torch.sum(task_attention * torch.log(task_attention + 1e-8), dim=-1)
            attention_reg_loss += -attention_entropy.mean()  # 负号因为我们想要低熵（稀疏）

            # 协作注意力正则化（鼓励平衡）
            if len(attention_results['collaboration_weights'].shape) == 4:
                collab_attention = attention_results['collaboration_weights'].mean(dim=-1)
            else:
                collab_attention = attention_results['collaboration_weights']

            # 鼓励协作注意力的均匀分布
            uniform_target = torch.ones_like(collab_attention) / collab_attention.shape[-1]
            attention_reg_loss += F.kl_div(
                torch.log(collab_attention + 1e-8), uniform_target, reduction='batchmean'
            )

        attention_reg_loss *= self.attention_reg_coef

        # 4. 时序一致性损失
        temporal_loss = torch.tensor(0.0, device=self.device)
        if old_attention_weights is not None and 'attention_results' in current_output:
            current_attention = current_output['attention_results']['task_attention_weights']
            if current_attention.shape == old_attention_weights.shape:
                temporal_loss = F.mse_loss(current_attention, old_attention_weights)
                temporal_loss *= self.temporal_consistency_coef

        # 总策略损失
        total_policy_loss = ppo_loss + entropy_loss + attention_reg_loss + temporal_loss

        return {
            'ppo_loss': ppo_loss,
            'entropy_loss': entropy_loss,
            'attention_reg_loss': attention_reg_loss,
            'temporal_loss': temporal_loss,
            'total_policy_loss': total_policy_loss,
            'ratio_mean': ratio.mean(),
            'ratio_std': ratio.std(),
            'advantages_mean': advantages.mean(),
            'advantages_std': advantages.std()
        }

    def compute_value_loss(self, current_output: Dict[str, torch.Tensor],
                          target_values: torch.Tensor, old_values: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算价值损失函数

        Args:
            current_output: 当前网络输出
            target_values: 目标价值 (batch_size, num_agvs)
            old_values: 旧的价值估计 (batch_size, num_agvs)

        Returns:
            Dict包含各种价值损失组件
        """
        # 1. 基础价值损失
        individual_values = current_output['individual_values']
        global_value = current_output['global_value']
        fused_values = current_output['fused_values']

        # 个体价值损失（MSE）
        individual_value_loss = F.mse_loss(individual_values, target_values)

        # 全局价值损失（使用目标值的平均）
        target_global = target_values.mean(dim=-1)  # (batch_size,)
        global_value_loss = F.mse_loss(global_value, target_global)

        # 融合价值损失（Huber损失，更稳定）
        fused_value_loss = F.smooth_l1_loss(fused_values, target_values)

        # 2. 价值裁剪损失（类似PPO的价值裁剪）
        value_pred_clipped = old_values + torch.clamp(
            individual_values - old_values, -self.clip_ratio, self.clip_ratio
        )
        value_losses = (individual_values - target_values) ** 2
        value_losses_clipped = (value_pred_clipped - target_values) ** 2
        value_clip_loss = torch.max(value_losses, value_losses_clipped).mean()

        # 3. 注意力一致性损失
        consistency_loss = torch.tensor(0.0, device=self.device)
        if 'consistency_scores' in current_output:
            consistency_scores = current_output['consistency_scores']
            # 鼓励高一致性（接近1）
            target_consistency = torch.ones_like(consistency_scores)
            consistency_loss = F.binary_cross_entropy_with_logits(
                consistency_scores, target_consistency
            )
            consistency_loss *= self.consistency_coef

        # 4. 价值函数间的一致性损失
        value_consistency_loss = torch.tensor(0.0, device=self.device)
        # 个体价值和融合价值应该相近
        value_consistency_loss += F.mse_loss(individual_values, fused_values)
        # 全局价值应该与个体价值的平均相近
        value_consistency_loss += F.mse_loss(global_value, individual_values.mean(dim=-1))
        value_consistency_loss *= 0.1  # 较小的权重

        # 总价值损失
        total_value_loss = (individual_value_loss + global_value_loss + fused_value_loss +
                           value_clip_loss + consistency_loss + value_consistency_loss)

        return {
            'individual_value_loss': individual_value_loss,
            'global_value_loss': global_value_loss,
            'fused_value_loss': fused_value_loss,
            'value_clip_loss': value_clip_loss,
            'consistency_loss': consistency_loss,
            'value_consistency_loss': value_consistency_loss,
            'total_value_loss': total_value_loss
        }

    def compute_total_loss(self, policy_losses: Dict[str, torch.Tensor],
                          value_losses: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        计算多智能体总损失函数

        Args:
            policy_losses: 策略损失字典
            value_losses: 价值损失字典

        Returns:
            Dict包含总损失和统计信息
        """
        # 总损失 = 策略损失 + 价值损失系数 * 价值损失
        total_loss = policy_losses['total_policy_loss'] + self.value_coef * value_losses['total_value_loss']

        # 损失统计
        loss_stats = {
            'total_loss': total_loss,
            'policy_loss': policy_losses['total_policy_loss'],
            'value_loss': value_losses['total_value_loss'],
            'ppo_loss': policy_losses['ppo_loss'],
            'entropy_loss': policy_losses['entropy_loss'],
            'attention_reg_loss': policy_losses['attention_reg_loss'],
            'temporal_loss': policy_losses['temporal_loss'],
            'individual_value_loss': value_losses['individual_value_loss'],
            'global_value_loss': value_losses['global_value_loss'],
            'fused_value_loss': value_losses['fused_value_loss'],
            'consistency_loss': value_losses['consistency_loss']
        }

        return loss_stats

    def get_loss_statistics(self, loss_dict: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """获取损失统计信息"""
        stats = {}
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                stats[key] = float(value.item())
            else:
                stats[key] = float(value)
        return stats

class MAPPOTrainer:
    """MAPPO训练器"""

    def __init__(self, mappo_system: MAPPOWithDualAttention,
                 loss_function: MAPPOLossFunction,
                 learning_rate: float = 3e-4, weight_decay: float = 1e-5,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):
        self.mappo_system = mappo_system
        self.loss_function = loss_function
        self.device = device

        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch不可用，无法创建MAPPOTrainer")

        # 优化器
        self.optimizer = torch.optim.Adam(
            mappo_system.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.StepLR(
            self.optimizer, step_size=1000, gamma=0.95
        )

        # 训练统计
        self.training_stats = {
            'total_steps': 0,
            'total_episodes': 0,
            'loss_history': [],
            'reward_history': []
        }

    def compute_advantages(self, rewards: torch.Tensor, values: torch.Tensor,
                          next_values: torch.Tensor, dones: torch.Tensor,
                          gamma: float = 0.99, gae_lambda: float = 0.95) -> torch.Tensor:
        """
        计算GAE优势函数

        Args:
            rewards: 奖励 (batch_size, num_agvs)
            values: 价值估计 (batch_size, num_agvs)
            next_values: 下一步价值估计 (batch_size, num_agvs)
            dones: 结束标志 (batch_size, num_agvs)
            gamma: 折扣因子
            gae_lambda: GAE参数

        Returns:
            advantages: 优势函数 (batch_size, num_agvs)
        """
        batch_size, num_agvs = rewards.shape
        advantages = torch.zeros_like(rewards)

        # 计算TD误差
        td_errors = rewards + gamma * next_values * (1 - dones) - values

        # GAE计算
        gae = 0
        for t in reversed(range(batch_size)):
            if t == batch_size - 1:
                next_non_terminal = 1 - dones[t]
                next_gae = 0
            else:
                next_non_terminal = 1 - dones[t]
                next_gae = advantages[t + 1]

            gae = td_errors[t] + gamma * gae_lambda * next_non_terminal * next_gae
            advantages[t] = gae

        return advantages

    def train_step(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        执行一步训练

        Args:
            batch_data: 批次数据，包含状态、动作、奖励等

        Returns:
            训练统计信息
        """
        # 提取批次数据
        agv_features = batch_data['agv_features']
        task_features = batch_data['task_features']
        agv_positions = batch_data['agv_positions']
        task_constraints = batch_data.get('task_constraints', None)
        collaboration_constraints = batch_data.get('collaboration_constraints', None)
        action_masks = batch_data.get('action_masks', None)

        old_log_probs = batch_data['old_log_probs']
        old_values = batch_data['old_values']
        rewards = batch_data['rewards']
        next_values = batch_data['next_values']
        dones = batch_data['dones']
        old_attention_weights = batch_data.get('old_attention_weights', None)

        # 前向传播
        current_output = self.mappo_system(
            agv_features, task_features, agv_positions,
            task_constraints, collaboration_constraints, action_masks
        )

        # 计算优势函数
        advantages = self.compute_advantages(
            rewards, old_values, next_values, dones
        )

        # 标准化优势函数
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # 计算目标价值
        target_values = advantages + old_values

        # 计算损失
        policy_losses = self.loss_function.compute_policy_loss(
            current_output, old_log_probs, advantages, old_attention_weights
        )

        value_losses = self.loss_function.compute_value_loss(
            current_output, target_values, old_values
        )

        total_losses = self.loss_function.compute_total_loss(policy_losses, value_losses)

        # 反向传播
        self.optimizer.zero_grad()
        total_losses['total_loss'].backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.mappo_system.parameters(), max_norm=0.5)

        self.optimizer.step()
        self.scheduler.step()

        # 更新统计
        self.training_stats['total_steps'] += 1

        # 获取损失统计
        loss_stats = self.loss_function.get_loss_statistics(total_losses)
        loss_stats.update(self.loss_function.get_loss_statistics(policy_losses))
        loss_stats.update(self.loss_function.get_loss_statistics(value_losses))

        # 添加训练统计
        loss_stats['learning_rate'] = self.scheduler.get_last_lr()[0]
        loss_stats['grad_norm'] = torch.nn.utils.clip_grad_norm_(
            self.mappo_system.parameters(), max_norm=float('inf')
        ).item()

        return loss_stats

    def save_checkpoint(self, filepath: str, episode: int, total_reward: float):
        """保存检查点"""
        checkpoint = {
            'episode': episode,
            'total_reward': total_reward,
            'model_state_dict': self.mappo_system.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'training_stats': self.training_stats
        }
        torch.save(checkpoint, filepath)

    def load_checkpoint(self, filepath: str):
        """加载检查点"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.mappo_system.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.training_stats = checkpoint['training_stats']
        return checkpoint['episode'], checkpoint['total_reward']

# ================================
# 第六部分：训练策略与优化算法
# ================================

class MultiAgentExperience:
    """多智能体经验数据结构"""

    def __init__(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                 agv_positions: torch.Tensor, actions: Dict[str, torch.Tensor],
                 rewards: torch.Tensor, next_agv_features: torch.Tensor,
                 next_task_features: torch.Tensor, next_agv_positions: torch.Tensor,
                 dones: torch.Tensor, log_probs: torch.Tensor, values: torch.Tensor,
                 attention_weights: Dict[str, torch.Tensor], constraints: Dict,
                 priority: float = 1.0, timestamp: float = 0.0):

        self.agv_features = agv_features
        self.task_features = task_features
        self.agv_positions = agv_positions
        self.actions = actions  # {'high_level': tensor, 'low_level': tensor}
        self.rewards = rewards
        self.next_agv_features = next_agv_features
        self.next_task_features = next_task_features
        self.next_agv_positions = next_agv_positions
        self.dones = dones
        self.log_probs = log_probs
        self.values = values
        self.attention_weights = attention_weights  # {'task_attention': tensor, 'collaboration': tensor}
        self.constraints = constraints
        self.priority = priority
        self.timestamp = timestamp

        # 计算经验的复杂度指标
        self.complexity_score = self._compute_complexity_score()

    def _compute_complexity_score(self) -> float:
        """计算经验的复杂度分数"""
        complexity = 0.0

        # 1. 奖励方差（高方差表示复杂情况）
        reward_variance = float(self.rewards.var().item())
        complexity += reward_variance * 0.3

        # 2. 注意力权重熵（低熵表示明确决策，高熵表示复杂决策）
        if 'task_attention' in self.attention_weights:
            task_attention = self.attention_weights['task_attention']
            attention_entropy = -torch.sum(task_attention * torch.log(task_attention + 1e-8), dim=-1).mean()
            complexity += float(attention_entropy.item()) * 0.4

        # 3. 动作分布熵
        if 'high_level' in self.actions and 'low_level' in self.actions:
            # 简化：使用动作的标准差作为复杂度指标
            high_std = float(self.actions['high_level'].float().std().item())
            low_std = float(self.actions['low_level'].float().std().item())
            complexity += (high_std + low_std) * 0.3

        return complexity

    def to_device(self, device: torch.device):
        """将经验数据移动到指定设备"""
        self.agv_features = self.agv_features.to(device)
        self.task_features = self.task_features.to(device)
        self.agv_positions = self.agv_positions.to(device)

        for key in self.actions:
            self.actions[key] = self.actions[key].to(device)

        self.rewards = self.rewards.to(device)
        self.next_agv_features = self.next_agv_features.to(device)
        self.next_task_features = self.next_task_features.to(device)
        self.next_agv_positions = self.next_agv_positions.to(device)
        self.dones = self.dones.to(device)
        self.log_probs = self.log_probs.to(device)
        self.values = self.values.to(device)

        for key in self.attention_weights:
            self.attention_weights[key] = self.attention_weights[key].to(device)

class PrioritizedExperienceReplay:
    """优先级经验回放机制"""

    def __init__(self, capacity: int = 10000, alpha: float = 0.6, beta: float = 0.4,
                 beta_increment: float = 0.001, attention_weight: float = 0.3,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):

        self.capacity = capacity
        self.alpha = alpha  # 优先级指数
        self.beta = beta    # 重要性采样指数
        self.beta_increment = beta_increment
        self.attention_weight = attention_weight  # 注意力感知权重
        self.device = device

        # 经验存储
        self.experiences = []
        self.priorities = np.zeros(capacity, dtype=np.float32)
        self.position = 0
        self.size = 0

        # 注意力感知的经验筛选参数
        self.attention_threshold = 0.1  # 注意力权重变化阈值
        self.complexity_threshold = 1.0  # 复杂度阈值

        # 统计信息
        self.stats = {
            'total_added': 0,
            'total_sampled': 0,
            'avg_priority': 0.0,
            'attention_filtered': 0,
            'complexity_filtered': 0
        }

    def add(self, experience: MultiAgentExperience):
        """添加经验到回放缓冲区"""
        # 注意力感知的经验筛选
        if not self._should_store_experience(experience):
            return

        # 计算初始优先级
        priority = self._compute_initial_priority(experience)

        # 存储经验
        if self.size < self.capacity:
            self.experiences.append(experience)
            self.size += 1
        else:
            self.experiences[self.position] = experience

        self.priorities[self.position] = priority
        self.position = (self.position + 1) % self.capacity

        # 更新统计
        self.stats['total_added'] += 1
        self.stats['avg_priority'] = np.mean(self.priorities[:self.size])

    def sample(self, batch_size: int) -> Tuple[List[MultiAgentExperience], np.ndarray, np.ndarray]:
        """采样经验批次"""
        if self.size < batch_size:
            raise ValueError(f"缓冲区大小({self.size})小于批次大小({batch_size})")

        # 计算采样概率
        priorities = self.priorities[:self.size] ** self.alpha
        probabilities = priorities / priorities.sum()

        # 采样索引
        indices = np.random.choice(self.size, batch_size, p=probabilities)

        # 计算重要性采样权重
        weights = (self.size * probabilities[indices]) ** (-self.beta)
        weights = weights / weights.max()  # 归一化

        # 获取经验
        experiences = [self.experiences[idx] for idx in indices]

        # 更新beta
        self.beta = min(1.0, self.beta + self.beta_increment)

        # 更新统计
        self.stats['total_sampled'] += batch_size

        return experiences, indices, weights

    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray,
                         attention_changes: Optional[np.ndarray] = None):
        """更新经验优先级"""
        for i, idx in enumerate(indices):
            # 基础TD误差优先级
            td_priority = abs(td_errors[i]) + 1e-6

            # 注意力感知优先级调整
            attention_priority = 1.0
            if attention_changes is not None:
                attention_priority = 1.0 + self.attention_weight * attention_changes[i]

            # 复杂度优先级调整
            complexity_priority = 1.0 + 0.1 * self.experiences[idx].complexity_score

            # 综合优先级
            total_priority = td_priority * attention_priority * complexity_priority
            self.priorities[idx] = total_priority

        # 更新平均优先级统计
        self.stats['avg_priority'] = np.mean(self.priorities[:self.size])

    def _should_store_experience(self, experience: MultiAgentExperience) -> bool:
        """注意力感知的经验筛选"""
        # 1. 复杂度筛选
        if experience.complexity_score < self.complexity_threshold:
            self.stats['complexity_filtered'] += 1
            return False

        # 2. 注意力变化筛选（如果有历史注意力权重）
        if self.size > 0:
            # 获取最近的经验进行比较
            recent_exp = self.experiences[(self.position - 1) % self.capacity]
            if 'task_attention' in experience.attention_weights and 'task_attention' in recent_exp.attention_weights:
                current_attention = experience.attention_weights['task_attention']
                recent_attention = recent_exp.attention_weights['task_attention']

                # 计算注意力权重变化
                attention_change = torch.abs(current_attention - recent_attention).mean().item()

                if attention_change < self.attention_threshold:
                    self.stats['attention_filtered'] += 1
                    return False

        return True

    def _compute_initial_priority(self, experience: MultiAgentExperience) -> float:
        """计算经验的初始优先级"""
        # 基础优先级：奖励的绝对值
        reward_priority = float(torch.abs(experience.rewards).mean().item()) + 1e-6

        # 复杂度优先级
        complexity_priority = 1.0 + 0.1 * experience.complexity_score

        # 时间衰减（新经验优先级更高）
        time_priority = 1.0  # 可以根据timestamp计算时间衰减

        return reward_priority * complexity_priority * time_priority

    def get_statistics(self) -> Dict[str, float]:
        """获取回放缓冲区统计信息"""
        stats = self.stats.copy()
        stats['buffer_size'] = self.size
        stats['buffer_capacity'] = self.capacity
        stats['buffer_utilization'] = self.size / self.capacity
        stats['current_beta'] = self.beta

        if self.size > 0:
            complexities = [exp.complexity_score for exp in self.experiences[:self.size]]
            stats['avg_complexity'] = np.mean(complexities)
            stats['max_complexity'] = np.max(complexities)
            stats['min_complexity'] = np.min(complexities)

        return stats

    def clear(self):
        """清空回放缓冲区"""
        self.experiences.clear()
        self.priorities.fill(0)
        self.position = 0
        self.size = 0

        # 重置统计
        for key in self.stats:
            self.stats[key] = 0

class AdaptiveCurriculumLearning:
    """自适应课程学习策略"""

    def __init__(self, initial_difficulty: float = 0.3, max_difficulty: float = 1.0,
                 difficulty_increment: float = 0.05, success_threshold: float = 0.7,
                 failure_threshold: float = 0.3, adaptation_window: int = 100,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):

        self.current_difficulty = initial_difficulty
        self.max_difficulty = max_difficulty
        self.difficulty_increment = difficulty_increment
        self.success_threshold = success_threshold
        self.failure_threshold = failure_threshold
        self.adaptation_window = adaptation_window
        self.device = device

        # 性能历史记录
        self.performance_history = []
        self.difficulty_history = []

        # 课程学习参数
        self.curriculum_params = {
            'num_tasks': 8,      # 初始任务数量
            'max_tasks': 16,     # 最大任务数量
            'agv_count': 2,      # 初始AGV数量
            'max_agvs': 4,       # 最大AGV数量
            'map_complexity': 0.3,  # 地图复杂度
            'task_urgency': 0.5,    # 任务紧急度
            'collaboration_requirement': 0.3  # 协作需求度
        }

        # 统计信息
        self.stats = {
            'total_adaptations': 0,
            'difficulty_increases': 0,
            'difficulty_decreases': 0,
            'current_success_rate': 0.0,
            'avg_performance': 0.0
        }

    def update_performance(self, episode_reward: float, episode_success: bool,
                          attention_quality: float = 0.0):
        """更新性能记录"""
        # 计算综合性能分数
        performance_score = self._compute_performance_score(
            episode_reward, episode_success, attention_quality
        )

        self.performance_history.append(performance_score)
        self.difficulty_history.append(self.current_difficulty)

        # 保持历史记录在窗口大小内
        if len(self.performance_history) > self.adaptation_window:
            self.performance_history.pop(0)
            self.difficulty_history.pop(0)

        # 自适应调整难度
        if len(self.performance_history) >= 10:  # 至少需要10个样本
            self._adapt_difficulty()

        # 更新统计
        self.stats['current_success_rate'] = np.mean([p > 0.5 for p in self.performance_history[-20:]])
        self.stats['avg_performance'] = np.mean(self.performance_history[-20:])

    def get_current_curriculum(self) -> Dict[str, any]:
        """获取当前课程配置"""
        # 根据当前难度调整课程参数
        curriculum = self.curriculum_params.copy()

        # 任务数量随难度增加
        curriculum['num_tasks'] = int(
            self.curriculum_params['num_tasks'] +
            (self.curriculum_params['max_tasks'] - self.curriculum_params['num_tasks']) * self.current_difficulty
        )

        # AGV数量随难度增加
        curriculum['agv_count'] = int(
            self.curriculum_params['agv_count'] +
            (self.curriculum_params['max_agvs'] - self.curriculum_params['agv_count']) * self.current_difficulty
        )

        # 地图复杂度
        curriculum['map_complexity'] = min(1.0,
            self.curriculum_params['map_complexity'] + 0.7 * self.current_difficulty
        )

        # 任务紧急度
        curriculum['task_urgency'] = min(1.0,
            self.curriculum_params['task_urgency'] + 0.5 * self.current_difficulty
        )

        # 协作需求度
        curriculum['collaboration_requirement'] = min(1.0,
            self.curriculum_params['collaboration_requirement'] + 0.7 * self.current_difficulty
        )

        return curriculum

    def _compute_performance_score(self, episode_reward: float, episode_success: bool,
                                  attention_quality: float) -> float:
        """计算综合性能分数"""
        # 归一化奖励分数（假设奖励范围在-10到10之间）
        reward_score = (episode_reward + 10) / 20
        reward_score = np.clip(reward_score, 0, 1)

        # 成功分数
        success_score = 1.0 if episode_success else 0.0

        # 注意力质量分数（0-1范围）
        attention_score = np.clip(attention_quality, 0, 1)

        # 综合分数
        performance_score = (0.4 * reward_score + 0.4 * success_score + 0.2 * attention_score)

        return performance_score

    def _adapt_difficulty(self):
        """自适应调整难度"""
        recent_performance = np.mean(self.performance_history[-20:])  # 最近20个episode的平均性能

        old_difficulty = self.current_difficulty

        # 根据性能调整难度
        if recent_performance > self.success_threshold:
            # 性能良好，增加难度
            self.current_difficulty = min(
                self.max_difficulty,
                self.current_difficulty + self.difficulty_increment
            )
            if self.current_difficulty > old_difficulty:
                self.stats['difficulty_increases'] += 1

        elif recent_performance < self.failure_threshold:
            # 性能较差，降低难度
            self.current_difficulty = max(
                0.1,  # 最小难度
                self.current_difficulty - self.difficulty_increment
            )
            if self.current_difficulty < old_difficulty:
                self.stats['difficulty_decreases'] += 1

        # 记录调整
        if self.current_difficulty != old_difficulty:
            self.stats['total_adaptations'] += 1

    def get_attention_curriculum_weights(self) -> Dict[str, float]:
        """获取注意力机制的课程学习权重"""
        # 根据难度调整注意力机制的复杂度
        weights = {
            'task_attention_complexity': 0.5 + 0.5 * self.current_difficulty,
            'collaboration_complexity': 0.3 + 0.7 * self.current_difficulty,
            'temporal_consistency_weight': 0.1 + 0.4 * self.current_difficulty,
            'attention_regularization': 0.05 + 0.15 * self.current_difficulty
        }

        return weights

    def should_use_advanced_features(self) -> Dict[str, bool]:
        """判断是否应该使用高级特性"""
        return {
            'use_collaboration_attention': self.current_difficulty > 0.4,
            'use_temporal_consistency': self.current_difficulty > 0.5,
            'use_attention_regularization': self.current_difficulty > 0.6,
            'use_hierarchical_actions': self.current_difficulty > 0.7
        }

    def get_statistics(self) -> Dict[str, any]:
        """获取课程学习统计信息"""
        stats = self.stats.copy()
        stats['current_difficulty'] = self.current_difficulty
        stats['performance_history_length'] = len(self.performance_history)

        if len(self.performance_history) > 0:
            stats['best_performance'] = np.max(self.performance_history)
            stats['worst_performance'] = np.min(self.performance_history)
            stats['performance_std'] = np.std(self.performance_history)

        return stats

    def reset(self):
        """重置课程学习状态"""
        self.current_difficulty = 0.3
        self.performance_history.clear()
        self.difficulty_history.clear()

        # 重置统计
        for key in self.stats:
            if isinstance(self.stats[key], (int, float)):
                self.stats[key] = 0

    def get_multi_dimensional_difficulty(self) -> Dict[str, float]:
        """获取多维度难度评估"""
        # 基于当前难度计算各维度的具体难度
        base_difficulty = self.current_difficulty

        # 任务复杂度维度
        task_complexity = {
            'task_density': min(1.0, 0.3 + 0.7 * base_difficulty),  # 任务密度
            'task_urgency': min(1.0, 0.2 + 0.8 * base_difficulty),  # 任务紧急度
            'task_weight_variance': min(1.0, 0.1 + 0.9 * base_difficulty),  # 任务重量方差
            'task_spatial_distribution': min(1.0, 0.4 + 0.6 * base_difficulty)  # 任务空间分布复杂度
        }

        # 协作复杂度维度
        collaboration_complexity = {
            'agv_interaction_frequency': min(1.0, 0.2 + 0.8 * base_difficulty),  # AGV交互频率
            'resource_competition': min(1.0, 0.1 + 0.9 * base_difficulty),  # 资源竞争强度
            'coordination_requirement': min(1.0, 0.3 + 0.7 * base_difficulty),  # 协调需求
            'conflict_resolution_complexity': min(1.0, 0.2 + 0.8 * base_difficulty)  # 冲突解决复杂度
        }

        # 环境复杂度维度
        environment_complexity = {
            'map_layout_complexity': min(1.0, 0.3 + 0.7 * base_difficulty),  # 地图布局复杂度
            'dynamic_obstacles': min(1.0, 0.0 + 1.0 * base_difficulty),  # 动态障碍物
            'path_planning_difficulty': min(1.0, 0.2 + 0.8 * base_difficulty),  # 路径规划难度
            'communication_constraints': min(1.0, 0.1 + 0.9 * base_difficulty)  # 通信约束
        }

        # 注意力机制复杂度维度
        attention_complexity = {
            'attention_head_count': min(1.0, 0.5 + 0.5 * base_difficulty),  # 注意力头数量
            'attention_regularization_strength': min(1.0, 0.1 + 0.9 * base_difficulty),  # 正则化强度
            'temporal_consistency_requirement': min(1.0, 0.2 + 0.8 * base_difficulty),  # 时序一致性要求
            'multi_layer_interaction': min(1.0, 0.3 + 0.7 * base_difficulty)  # 多层交互复杂度
        }

        return {
            'task_complexity': task_complexity,
            'collaboration_complexity': collaboration_complexity,
            'environment_complexity': environment_complexity,
            'attention_complexity': attention_complexity,
            'overall_difficulty': base_difficulty
        }

    def get_skill_decomposition_curriculum(self) -> Dict[str, Dict[str, float]]:
        """获取技能分解课程学习配置"""
        difficulty = self.current_difficulty

        # 基础技能：单AGV任务执行
        basic_skills = {
            'single_task_execution': max(0.0, 1.0 - difficulty * 0.5),  # 难度增加时减少单任务权重
            'path_planning': 0.8 + 0.2 * difficulty,  # 路径规划始终重要
            'obstacle_avoidance': 0.6 + 0.4 * difficulty,  # 障碍物避免
            'load_management': 0.4 + 0.6 * difficulty  # 载重管理
        }

        # 中级技能：多任务协调
        intermediate_skills = {
            'multi_task_scheduling': max(0.0, difficulty - 0.3) * 1.5,  # 难度>0.3时开始
            'resource_allocation': max(0.0, difficulty - 0.2) * 1.2,  # 资源分配
            'priority_management': max(0.0, difficulty - 0.4) * 1.8,  # 优先级管理
            'dynamic_replanning': max(0.0, difficulty - 0.5) * 2.0  # 动态重规划
        }

        # 高级技能：多AGV协作
        advanced_skills = {
            'inter_agv_communication': max(0.0, difficulty - 0.4) * 1.5,  # AGV间通信
            'collaborative_task_execution': max(0.0, difficulty - 0.5) * 2.0,  # 协作任务执行
            'conflict_resolution': max(0.0, difficulty - 0.6) * 2.5,  # 冲突解决
            'emergent_coordination': max(0.0, difficulty - 0.7) * 3.0  # 涌现式协调
        }

        # 专家技能：复杂场景处理
        expert_skills = {
            'complex_scenario_handling': max(0.0, difficulty - 0.7) * 2.0,  # 复杂场景处理
            'adaptive_strategy_selection': max(0.0, difficulty - 0.8) * 2.5,  # 自适应策略选择
            'system_optimization': max(0.0, difficulty - 0.8) * 2.0,  # 系统优化
            'fault_tolerance': max(0.0, difficulty - 0.9) * 3.0  # 容错能力
        }

        return {
            'basic_skills': basic_skills,
            'intermediate_skills': intermediate_skills,
            'advanced_skills': advanced_skills,
            'expert_skills': expert_skills
        }

    def get_progressive_learning_schedule(self) -> Dict[str, any]:
        """获取渐进学习时间表"""
        difficulty = self.current_difficulty

        # 学习阶段定义
        if difficulty < 0.3:
            current_stage = "基础技能学习"
            focus_areas = ["single_task_execution", "path_planning", "obstacle_avoidance"]
            next_milestone = 0.3
        elif difficulty < 0.5:
            current_stage = "中级技能发展"
            focus_areas = ["multi_task_scheduling", "resource_allocation", "priority_management"]
            next_milestone = 0.5
        elif difficulty < 0.7:
            current_stage = "高级协作训练"
            focus_areas = ["inter_agv_communication", "collaborative_task_execution", "conflict_resolution"]
            next_milestone = 0.7
        else:
            current_stage = "专家级优化"
            focus_areas = ["complex_scenario_handling", "adaptive_strategy_selection", "system_optimization"]
            next_milestone = 1.0

        # 学习进度
        stage_progress = self._calculate_stage_progress(difficulty)

        # 预期学习时间
        estimated_episodes_to_next = max(10, int((next_milestone - difficulty) / self.difficulty_increment * 20))

        return {
            'current_stage': current_stage,
            'stage_progress': stage_progress,
            'focus_areas': focus_areas,
            'next_milestone': next_milestone,
            'estimated_episodes_to_next': estimated_episodes_to_next,
            'learning_efficiency': self._calculate_learning_efficiency()
        }

    def _calculate_stage_progress(self, difficulty: float) -> float:
        """计算当前阶段的学习进度"""
        if difficulty < 0.3:
            return difficulty / 0.3
        elif difficulty < 0.5:
            return (difficulty - 0.3) / 0.2
        elif difficulty < 0.7:
            return (difficulty - 0.5) / 0.2
        else:
            return (difficulty - 0.7) / 0.3

    def _calculate_learning_efficiency(self) -> float:
        """计算学习效率"""
        if len(self.performance_history) < 10:
            return 0.5

        # 计算最近性能的改善趋势
        recent_performance = self.performance_history[-10:]
        if len(recent_performance) < 2:
            return 0.5

        # 线性回归计算趋势
        x = np.arange(len(recent_performance))
        y = np.array(recent_performance)

        if len(x) > 1:
            slope = np.polyfit(x, y, 1)[0]
            # 将斜率转换为效率分数
            efficiency = min(1.0, max(0.0, 0.5 + slope * 10))
        else:
            efficiency = 0.5

        return efficiency

class TrainingStabilityManager:
    """训练稳定性保证机制"""

    def __init__(self, gradient_clip_norm: float = 0.5, loss_scale_factor: float = 1.0,
                 nan_detection_window: int = 10, stability_check_interval: int = 100,
                 device='cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'):

        self.gradient_clip_norm = gradient_clip_norm
        self.loss_scale_factor = loss_scale_factor
        self.nan_detection_window = nan_detection_window
        self.stability_check_interval = stability_check_interval
        self.device = device

        # 稳定性监控
        self.loss_history = []
        self.gradient_norm_history = []
        self.nan_count = 0
        self.inf_count = 0
        self.step_count = 0

        # 自适应参数
        self.adaptive_lr_factor = 1.0
        self.adaptive_clip_norm = gradient_clip_norm

        # 稳定性统计
        self.stats = {
            'total_nan_detections': 0,
            'total_inf_detections': 0,
            'gradient_explosions': 0,
            'loss_spikes': 0,
            'stability_interventions': 0,
            'current_stability_score': 1.0
        }

    def check_and_fix_gradients(self, model: nn.Module) -> Dict[str, float]:
        """检查和修复梯度问题"""
        gradient_stats = {
            'total_norm': 0.0,
            'max_grad': 0.0,
            'nan_grads': 0,
            'inf_grads': 0,
            'zero_grads': 0
        }

        total_norm = 0.0
        max_grad = 0.0
        nan_count = 0
        inf_count = 0
        zero_count = 0

        for param in model.parameters():
            if param.grad is not None:
                grad = param.grad.data

                # 检查NaN
                if torch.isnan(grad).any():
                    nan_count += 1
                    # 修复NaN梯度
                    grad[torch.isnan(grad)] = 0.0
                    self.stats['total_nan_detections'] += 1

                # 检查Inf
                if torch.isinf(grad).any():
                    inf_count += 1
                    # 修复Inf梯度
                    grad[torch.isinf(grad)] = 0.0
                    self.stats['total_inf_detections'] += 1

                # 检查零梯度
                if torch.all(grad == 0):
                    zero_count += 1

                # 计算梯度范数
                param_norm = grad.norm().item()
                total_norm += param_norm ** 2
                max_grad = max(max_grad, grad.abs().max().item())

        total_norm = total_norm ** 0.5

        # 自适应梯度裁剪
        if total_norm > self.adaptive_clip_norm * 2:
            self.stats['gradient_explosions'] += 1
            # 动态调整裁剪范数
            self.adaptive_clip_norm = max(0.1, self.adaptive_clip_norm * 0.9)
        elif total_norm < self.adaptive_clip_norm * 0.5:
            # 逐渐恢复裁剪范数
            self.adaptive_clip_norm = min(self.gradient_clip_norm, self.adaptive_clip_norm * 1.01)

        # 应用梯度裁剪
        if total_norm > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), self.adaptive_clip_norm)

        # 更新统计
        gradient_stats['total_norm'] = total_norm
        gradient_stats['max_grad'] = max_grad
        gradient_stats['nan_grads'] = nan_count
        gradient_stats['inf_grads'] = inf_count
        gradient_stats['zero_grads'] = zero_count

        self.gradient_norm_history.append(total_norm)
        if len(self.gradient_norm_history) > 1000:
            self.gradient_norm_history.pop(0)

        return gradient_stats

    def check_loss_stability(self, loss: torch.Tensor) -> bool:
        """检查损失稳定性"""
        loss_value = loss.item()

        # 检查NaN和Inf
        if np.isnan(loss_value) or np.isinf(loss_value):
            self.stats['total_nan_detections'] += 1
            return False

        self.loss_history.append(loss_value)
        if len(self.loss_history) > 1000:
            self.loss_history.pop(0)

        # 检查损失突增
        if len(self.loss_history) > 10:
            recent_avg = np.mean(self.loss_history[-10:])
            historical_avg = np.mean(self.loss_history[:-10]) if len(self.loss_history) > 10 else recent_avg

            if recent_avg > historical_avg * 3:  # 损失突增3倍
                self.stats['loss_spikes'] += 1
                return False

        return True

    def get_adaptive_learning_rate(self, base_lr: float) -> float:
        """获取自适应学习率"""
        # 基于梯度范数历史调整学习率
        if len(self.gradient_norm_history) > 20:
            recent_grad_norm = np.mean(self.gradient_norm_history[-20:])

            if recent_grad_norm > 1.0:
                # 梯度较大，降低学习率
                self.adaptive_lr_factor = max(0.1, self.adaptive_lr_factor * 0.95)
            elif recent_grad_norm < 0.1:
                # 梯度较小，可以适当提高学习率
                self.adaptive_lr_factor = min(2.0, self.adaptive_lr_factor * 1.01)

        return base_lr * self.adaptive_lr_factor

    def should_skip_update(self) -> bool:
        """判断是否应该跳过参数更新"""
        # 检查最近的NaN/Inf计数
        recent_window = min(self.nan_detection_window, len(self.loss_history))
        if recent_window > 0:
            recent_losses = self.loss_history[-recent_window:]
            nan_ratio = sum(1 for loss in recent_losses if np.isnan(loss) or np.isinf(loss)) / recent_window

            if nan_ratio > 0.3:  # 30%的损失为NaN/Inf
                self.stats['stability_interventions'] += 1
                return True

        return False

    def compute_stability_score(self) -> float:
        """计算训练稳定性分数"""
        if len(self.loss_history) < 10:
            return 1.0

        stability_score = 1.0

        # 损失方差惩罚
        loss_std = np.std(self.loss_history[-50:]) if len(self.loss_history) >= 50 else 0
        stability_score -= min(0.3, loss_std / 10)

        # 梯度范数稳定性
        if len(self.gradient_norm_history) >= 20:
            grad_std = np.std(self.gradient_norm_history[-20:])
            stability_score -= min(0.2, grad_std / 5)

        # NaN/Inf惩罚
        total_steps = max(1, len(self.loss_history))
        nan_ratio = self.stats['total_nan_detections'] / total_steps
        stability_score -= min(0.3, nan_ratio * 10)

        # 梯度爆炸惩罚
        explosion_ratio = self.stats['gradient_explosions'] / total_steps
        stability_score -= min(0.2, explosion_ratio * 20)

        stability_score = max(0.0, stability_score)
        self.stats['current_stability_score'] = stability_score

        return stability_score

    def get_regularization_weights(self) -> Dict[str, float]:
        """获取自适应正则化权重"""
        stability_score = self.compute_stability_score()

        # 稳定性越低，正则化越强
        regularization_factor = 2.0 - stability_score

        return {
            'attention_regularization': 0.1 * regularization_factor,
            'temporal_consistency': 0.05 * regularization_factor,
            'value_consistency': 0.1 * regularization_factor,
            'gradient_penalty': 0.01 * regularization_factor
        }

    def update_step(self):
        """更新步数计数"""
        self.step_count += 1

        # 定期计算稳定性分数
        if self.step_count % self.stability_check_interval == 0:
            self.compute_stability_score()

    def get_statistics(self) -> Dict[str, any]:
        """获取稳定性统计信息"""
        stats = self.stats.copy()
        stats['step_count'] = self.step_count
        stats['adaptive_lr_factor'] = self.adaptive_lr_factor
        stats['adaptive_clip_norm'] = self.adaptive_clip_norm

        if len(self.loss_history) > 0:
            stats['avg_loss'] = np.mean(self.loss_history[-100:])
            stats['loss_std'] = np.std(self.loss_history[-100:])

        if len(self.gradient_norm_history) > 0:
            stats['avg_grad_norm'] = np.mean(self.gradient_norm_history[-100:])
            stats['grad_norm_std'] = np.std(self.gradient_norm_history[-100:])

        return stats

    def reset(self):
        """重置稳定性管理器"""
        self.loss_history.clear()
        self.gradient_norm_history.clear()
        self.nan_count = 0
        self.inf_count = 0
        self.step_count = 0
        self.adaptive_lr_factor = 1.0
        self.adaptive_clip_norm = self.gradient_clip_norm

        # 重置统计
        for key in self.stats:
            if isinstance(self.stats[key], (int, float)):
                self.stats[key] = 0 if 'count' in key or 'total' in key else 1.0

    def get_hierarchical_learning_rates(self, base_lr: float) -> Dict[str, float]:
        """获取分层学习率调度"""
        stability_factor = self.compute_stability_score()

        # 不同模块的学习率调整
        learning_rates = {
            # 注意力机制相关参数（更保守的学习率）
            'attention_params': base_lr * 0.5 * stability_factor,
            'task_attention': base_lr * 0.3 * stability_factor,
            'collaboration_attention': base_lr * 0.4 * stability_factor,
            'attention_fusion': base_lr * 0.6 * stability_factor,

            # 策略网络参数
            'policy_backbone': base_lr * 0.8 * stability_factor,
            'policy_head': base_lr * 1.0 * stability_factor,
            'action_mask_generator': base_lr * 0.7 * stability_factor,

            # 价值网络参数
            'value_backbone': base_lr * 0.9 * stability_factor,
            'value_head': base_lr * 1.2 * stability_factor,
            'global_value_net': base_lr * 0.8 * stability_factor,

            # 嵌入层参数（最保守）
            'embedding_layers': base_lr * 0.2 * stability_factor,
            'feature_extractors': base_lr * 0.4 * stability_factor
        }

        return learning_rates

    def apply_advanced_gradient_clipping(self, model: nn.Module,
                                       module_specific_norms: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """应用高级梯度裁剪策略"""
        if module_specific_norms is None:
            module_specific_norms = {
                'attention': 0.3,
                'policy': 0.5,
                'value': 0.7,
                'embedding': 0.2
            }

        gradient_stats = {}

        # 分模块梯度裁剪
        for name, module in model.named_modules():
            if not list(module.parameters()):
                continue

            # 确定模块类型
            module_type = self._classify_module(name)
            clip_norm = module_specific_norms.get(module_type, self.adaptive_clip_norm)

            # 计算模块梯度范数
            module_params = [p for p in module.parameters() if p.grad is not None]
            if not module_params:
                continue

            total_norm = 0.0
            for param in module_params:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
            total_norm = total_norm ** 0.5

            # 应用裁剪
            if total_norm > clip_norm:
                clip_coef = clip_norm / (total_norm + 1e-6)
                for param in module_params:
                    param.grad.data.mul_(clip_coef)

                gradient_stats[f'{module_type}_clipped'] = True
                gradient_stats[f'{module_type}_clip_ratio'] = clip_coef
            else:
                gradient_stats[f'{module_type}_clipped'] = False
                gradient_stats[f'{module_type}_clip_ratio'] = 1.0

            gradient_stats[f'{module_type}_grad_norm'] = total_norm

        return gradient_stats

    def _classify_module(self, module_name: str) -> str:
        """分类模块类型"""
        name_lower = module_name.lower()

        if 'attention' in name_lower or 'attn' in name_lower:
            return 'attention'
        elif 'policy' in name_lower:
            return 'policy'
        elif 'value' in name_lower:
            return 'value'
        elif 'embedding' in name_lower or 'embed' in name_lower:
            return 'embedding'
        else:
            return 'other'

    def get_advanced_regularization_config(self) -> Dict[str, Dict[str, float]]:
        """获取高级正则化配置"""
        stability_score = self.compute_stability_score()
        instability_factor = 2.0 - stability_score  # 不稳定性因子

        # 注意力权重正则化
        attention_regularization = {
            'attention_entropy_penalty': 0.01 * instability_factor,  # 注意力熵惩罚
            'attention_sparsity_reward': 0.005 * instability_factor,  # 稀疏性奖励
            'attention_consistency_penalty': 0.02 * instability_factor,  # 一致性惩罚
            'cross_layer_attention_alignment': 0.01 * instability_factor  # 跨层对齐
        }

        # 参数共享正则化
        parameter_sharing_regularization = {
            'shared_embedding_consistency': 0.01 * instability_factor,  # 共享嵌入一致性
            'policy_value_alignment': 0.005 * instability_factor,  # 策略-价值对齐
            'multi_head_consistency': 0.008 * instability_factor  # 多头一致性
        }

        # 时序平滑正则化
        temporal_smoothing_regularization = {
            'temporal_attention_smoothing': 0.02 * instability_factor,  # 时序注意力平滑
            'policy_temporal_consistency': 0.01 * instability_factor,  # 策略时序一致性
            'value_temporal_stability': 0.015 * instability_factor,  # 价值时序稳定性
            'gradient_temporal_smoothing': 0.005 * instability_factor  # 梯度时序平滑
        }

        # 网络结构正则化
        structural_regularization = {
            'layer_norm_penalty': 0.001 * instability_factor,  # 层归一化惩罚
            'residual_connection_stability': 0.002 * instability_factor,  # 残差连接稳定性
            'activation_distribution_penalty': 0.003 * instability_factor  # 激活分布惩罚
        }

        return {
            'attention_regularization': attention_regularization,
            'parameter_sharing_regularization': parameter_sharing_regularization,
            'temporal_smoothing_regularization': temporal_smoothing_regularization,
            'structural_regularization': structural_regularization
        }

    def detect_training_anomalies(self, current_metrics: Dict[str, float]) -> Dict[str, any]:
        """训练异常检测"""
        anomalies = {
            'detected_anomalies': [],
            'severity_level': 'normal',  # normal, warning, critical
            'recommended_actions': []
        }

        # 检查梯度异常
        if 'grad_norm' in current_metrics:
            grad_norm = current_metrics['grad_norm']
            if grad_norm > 10.0:
                anomalies['detected_anomalies'].append('gradient_explosion')
                anomalies['severity_level'] = 'critical'
                anomalies['recommended_actions'].append('reduce_learning_rate')
                anomalies['recommended_actions'].append('increase_gradient_clipping')
            elif grad_norm < 1e-6:
                anomalies['detected_anomalies'].append('gradient_vanishing')
                anomalies['severity_level'] = 'warning'
                anomalies['recommended_actions'].append('increase_learning_rate')

        # 检查损失异常
        if len(self.loss_history) > 10:
            recent_losses = self.loss_history[-10:]
            loss_std = np.std(recent_losses)
            loss_mean = np.mean(recent_losses)

            if loss_std > loss_mean * 0.5:  # 损失方差过大
                anomalies['detected_anomalies'].append('loss_instability')
                if anomalies['severity_level'] == 'normal':
                    anomalies['severity_level'] = 'warning'
                anomalies['recommended_actions'].append('increase_regularization')

            if any(np.isnan(recent_losses)) or any(np.isinf(recent_losses)):
                anomalies['detected_anomalies'].append('nan_inf_loss')
                anomalies['severity_level'] = 'critical'
                anomalies['recommended_actions'].append('reset_optimizer_state')

        # 检查学习停滞
        if len(self.loss_history) > 50:
            recent_trend = np.polyfit(range(50), self.loss_history[-50:], 1)[0]
            if abs(recent_trend) < 1e-6:  # 学习停滞
                anomalies['detected_anomalies'].append('learning_stagnation')
                if anomalies['severity_level'] == 'normal':
                    anomalies['severity_level'] = 'warning'
                anomalies['recommended_actions'].append('adjust_curriculum_difficulty')
                anomalies['recommended_actions'].append('increase_exploration')

        return anomalies

    def get_adaptive_training_schedule(self) -> Dict[str, any]:
        """获取自适应训练调度"""
        stability_score = self.compute_stability_score()

        # 基于稳定性调整训练参数
        if stability_score > 0.8:
            # 高稳定性：可以加速训练
            schedule = {
                'learning_rate_multiplier': 1.2,
                'batch_size_multiplier': 1.1,
                'update_frequency': 1.0,
                'exploration_rate': 0.9,
                'regularization_strength': 0.8
            }
        elif stability_score > 0.6:
            # 中等稳定性：正常训练
            schedule = {
                'learning_rate_multiplier': 1.0,
                'batch_size_multiplier': 1.0,
                'update_frequency': 1.0,
                'exploration_rate': 1.0,
                'regularization_strength': 1.0
            }
        else:
            # 低稳定性：保守训练
            schedule = {
                'learning_rate_multiplier': 0.7,
                'batch_size_multiplier': 0.8,
                'update_frequency': 0.8,
                'exploration_rate': 1.2,
                'regularization_strength': 1.5
            }

        schedule['stability_score'] = stability_score
        schedule['recommended_checkpoint_frequency'] = max(100, int(1000 * (1 - stability_score)))

        return schedule

def test_visualization():
    """测试可视化功能并验证任务分布"""
    print("=" * 60)
    print("可视化验证测试")
    print("=" * 60)

    # 创建环境
    env = WarehouseEnvironment()
    obs = env.reset()

    print(f"环境配置验证:")
    print(f"  地图尺寸: {env.width} × {env.height}")
    print(f"  货架数量: {env.num_shelves}")
    print(f"  AGV数量: {env.num_agvs}")
    print(f"  任务数量: {env.num_tasks}")

    # 验证货架布局
    shelf_count = 0
    shelf_positions = []
    for y in range(env.height):
        for x in range(env.width):
            if env.grid[y, x] == 1:  # 货架
                shelf_count += 1
                shelf_positions.append((x, y))

    print(f"\n货架布局验证:")
    print(f"  实际货架格子数: {shelf_count}")
    print(f"  预期货架格子数: {15 * 4 * 2} (15个货架 × 4×2)")

    # 验证任务分布（检查任务位置是否原本是货架）
    print(f"\n任务分布验证:")
    tasks_on_shelves = 0
    tasks_in_passages = 0

    # 重新收集货架位置进行验证
    shelf_positions_for_verification = []
    for y in range(env.height):
        for x in range(env.width):
            # 检查原始货架布局（忽略AGV和任务标记）
            if env.grid[y, x] == 1 or env.grid[y, x] == 3:  # 货架或任务位置
                # 验证这个位置是否应该是货架
                is_shelf = False
                for row in range(3):
                    for col in range(5):
                        start_x = col * 5 + 1
                        start_y = row * 3 + 1
                        if (start_x <= x < start_x + 4 and
                            start_y <= y < start_y + 2):
                            is_shelf = True
                            break
                    if is_shelf:
                        break
                if is_shelf:
                    shelf_positions_for_verification.append((x, y))

    for i, task in enumerate(env.tasks):
        if (task.x, task.y) in shelf_positions_for_verification:  # 在货架位置上
            tasks_on_shelves += 1
            print(f"  ✅ 任务 {i}: 位置({task.x}, {task.y}) - 在货架上, 重量{task.weight}")
        else:  # 在通道中
            tasks_in_passages += 1
            print(f"  ❌ 任务 {i}: 位置({task.x}, {task.y}) - 在通道中, 重量{task.weight}")

    print(f"\n任务分布统计:")
    print(f"  货架上的任务: {tasks_on_shelves}/{env.num_tasks}")
    print(f"  通道中的任务: {tasks_in_passages}/{env.num_tasks}")

    if tasks_on_shelves == env.num_tasks:
        print(f"  ✅ 所有任务都正确分布在货架上!")
    else:
        print(f"  ❌ 有{tasks_in_passages}个任务错误地分布在通道中!")

    # 验证AGV初始位置（检查AGV位置是否在通道中，即不在货架上）
    print(f"\nAGV初始位置验证:")
    for i, agv in enumerate(env.agvs):
        # 检查AGV位置是否在货架区域
        is_on_shelf = False
        for row in range(3):
            for col in range(5):
                start_x = col * 5 + 1
                start_y = row * 3 + 1
                if (start_x <= agv.x < start_x + 4 and
                    start_y <= agv.y < start_y + 2):
                    is_on_shelf = True
                    break
            if is_on_shelf:
                break

        if not is_on_shelf:  # 不在货架上，即在通道中
            print(f"  ✅ AGV {i}: 位置({agv.x}, {agv.y}) - 在通道中")
        else:
            print(f"  ❌ AGV {i}: 位置({agv.x}, {agv.y}) - 在货架上")

    # 生成可视化
    print(f"\n生成可视化图片...")
    try:
        env.render(save_path='warehouse_visualization_test.png')
        print(f"  ✅ 可视化已保存到 warehouse_visualization_test.png")

        # 执行几步动作后再次可视化
        print(f"\n执行动作后的可视化...")
        actions = [(1, 1), (2, 2), (3, 3), (4, 0)]  # 简单的测试动作
        env.step(actions)
        env.render(save_path='warehouse_after_actions.png')
        print(f"  ✅ 动作后可视化已保存到 warehouse_after_actions.png")

    except Exception as e:
        print(f"  ❌ 可视化失败: {e}")

    print(f"\n可视化验证测试完成!")
    return env

def test_action_space_and_rewards():
    """测试动作空间和奖励函数实现"""
    print("=" * 60)
    print("测试动作空间与奖励函数实现")
    print("=" * 60)

    # 创建环境和相关组件
    env = WarehouseEnvironment()
    action_space = ActionSpace(num_tasks=16)
    reward_function = RewardFunction()

    print(f"动作空间配置:")
    print(f"  高层动作维度: {action_space.high_action_dim}")
    print(f"  低层动作维度: {action_space.low_action_dim}")
    print(f"  总动作组合数: {action_space.high_action_dim * action_space.low_action_dim}")

    # 测试动作描述
    print(f"\n动作描述示例:")
    test_actions = [(0, 4), (1, 0), (5, 2), (17, 1)]
    for high, low in test_actions:
        desc = action_space.get_action_description(high, low)
        valid = action_space.is_valid_action(high, low)
        print(f"  动作({high}, {low}): {desc} - {'有效' if valid else '无效'}")

    # 重置环境
    obs = env.reset()

    # 测试增强的动作掩码
    print(f"\n测试增强动作掩码:")
    enhanced_masks = env.get_enhanced_action_masks()
    for i, (high_mask, low_mask) in enumerate(enhanced_masks):
        valid_high = high_mask.sum()
        valid_low = low_mask.sum()
        print(f"  AGV {i}: 有效高层动作{valid_high}/18, 有效低层动作{valid_low}/5")

        # 显示被禁用的高层动作
        invalid_high = np.where(~high_mask)[0]
        if len(invalid_high) > 0:
            print(f"    禁用的高层动作: {invalid_high[:5]}{'...' if len(invalid_high) > 5 else ''}")

    # 测试奖励函数权重
    print(f"\n奖励函数权重配置:")
    for category, weight in reward_function.weights.items():
        print(f"  {category}: {weight}")

    # 执行几步动作并分析奖励分解
    print(f"\n执行动作并分析奖励分解:")
    for step in range(3):
        print(f"\n  === 步骤 {step + 1} ===")

        # 生成智能动作（基于掩码）
        actions = []
        for i, (high_mask, low_mask) in enumerate(enhanced_masks):
            high_action, low_action = action_space.sample_random_action(high_mask, low_mask)
            actions.append((high_action, low_action))

            desc = action_space.get_action_description(high_action, low_action)
            print(f"    AGV {i} 动作: {desc}")

        # 执行动作
        obs, rewards, done, info = env.step(actions)

        print(f"    奖励: {[f'{r:.3f}' for r in rewards]}")
        print(f"    环境信息: {info}")

        # 更新掩码
        enhanced_masks = env.get_enhanced_action_masks()

        if done:
            print(f"    回合结束!")
            break

    # 测试奖励分解功能
    print(f"\n测试奖励分解功能:")

    # 模拟任务完成奖励
    if env.tasks:
        task_reward_breakdown = reward_function.get_reward_breakdown(
            task_completion={
                'task': env.tasks[0],
                'timestep': env.total_timesteps
            }
        )
        print(f"  任务完成奖励分解: {task_reward_breakdown}")

    # 模拟移动奖励
    movement_reward_breakdown = reward_function.get_reward_breakdown(
        movement={
            'moved': True,
            'distance_improved': True,
            'distance_worsened': False
        }
    )
    print(f"  移动奖励分解: {movement_reward_breakdown}")

    # 模拟协作奖励
    collaboration_reward_breakdown = reward_function.get_reward_breakdown(
        collaboration={
            'collision_occurred': False,
            'collision_avoided': True
        }
    )
    print(f"  协作奖励分解: {collaboration_reward_breakdown}")

    # 模拟系统奖励
    system_reward_breakdown = reward_function.get_reward_breakdown(
        system={
            'load_balance_score': 0.8,
            'throughput': 0.1
        }
    )
    print(f"  系统奖励分解: {system_reward_breakdown}")

    print(f"\n动作空间与奖励函数测试完成!")
    return env, action_space, reward_function

def test_task_allocation_attention():
    """测试第一层任务分配注意力机制"""
    print("=" * 60)
    print("测试第一层任务分配注意力机制")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过注意力机制测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    # 创建环境和状态处理器
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)

    # 创建任务分配注意力机制
    task_attention = TaskAllocationAttention(
        embed_dim=64,
        num_heads=8,
        top_k=8,
        device=device
    )

    print(f"\n注意力机制配置:")
    print(f"  嵌入维度: {task_attention.embed_dim}")
    print(f"  注意力头数: {task_attention.num_heads}")
    print(f"  Top-K稀疏度: {task_attention.top_k}")
    print(f"  设备: {task_attention.device}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理全局状态
    print(f"\n状态处理测试:")
    print(f"  原始AGV特征形状: {global_state['agv_features'].shape}")
    print(f"  原始任务特征形状: {global_state['task_features'].shape}")

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    print(f"  全局嵌入状态形状: {global_embedded.shape}")

    # 分离AGV和任务特征
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    print(f"  AGV嵌入特征形状: {agv_embedded.shape}")
    print(f"  任务嵌入特征形状: {task_embedded.shape}")

    # 创建约束信息
    print(f"\n创建约束信息:")

    # 距离约束
    distances = np.zeros((1, 4, 16))
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)

    # 载重约束
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    # 任务可用性
    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])

    # 优先级（简化为随机值）
    priorities = np.random.uniform(0.5, 1.5, (1, 16))

    # 时间紧急度（简化为随机值）
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    print(f"  距离约束形状: {distances.shape}")
    print(f"  载重约束形状: {capacity_valid.shape}")
    print(f"  任务可用性形状: {task_available.shape}")
    print(f"  有效任务数量: {task_available.sum()}")

    # 前向传播测试
    print(f"\n前向传播测试:")
    try:
        with torch.no_grad():
            output, attention_weights, temporal_loss = task_attention(
                agv_embedded, task_embedded, constraints
            )

        print(f"  ✅ 前向传播成功")
        print(f"  输出形状: {output.shape}")
        print(f"  注意力权重形状: {attention_weights.shape}")
        print(f"  时序一致性损失: {temporal_loss.item():.6f}")

        # 分析注意力权重
        print(f"\n注意力权重分析:")
        attention_np = attention_weights.cpu().numpy()[0]  # (4, 16)

        for i in range(4):
            agv_attention = attention_np[i]
            top_tasks = np.argsort(agv_attention)[-3:][::-1]  # 前3个任务
            print(f"  AGV {i} 最关注的任务: {top_tasks} (权重: {agv_attention[top_tasks]})")

        # 测试稀疏化效果
        non_zero_weights = (attention_np > 1e-6).sum(axis=1)
        print(f"  每个AGV的非零注意力权重数量: {non_zero_weights}")
        print(f"  平均稀疏度: {non_zero_weights.mean():.1f}/{task_embedded.shape[2]}")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        return None

    # 测试时序一致性
    print(f"\n时序一致性测试:")
    try:
        with torch.no_grad():
            # 第二次前向传播
            output2, attention_weights2, temporal_loss2 = task_attention(
                agv_embedded, task_embedded, constraints
            )

        print(f"  第二次前向传播成功")
        print(f"  时序一致性损失: {temporal_loss2.item():.6f}")

        # 计算注意力权重变化
        attention_diff = torch.abs(attention_weights - attention_weights2).mean().item()
        print(f"  注意力权重平均变化: {attention_diff:.6f}")

    except Exception as e:
        print(f"  ❌ 时序一致性测试失败: {e}")

    # 重置时序状态测试
    print(f"\n重置时序状态测试:")
    task_attention.reset_temporal_state()
    print(f"  时序状态已重置")

    with torch.no_grad():
        output3, attention_weights3, temporal_loss3 = task_attention(
            agv_embedded, task_embedded, constraints
        )
    print(f"  重置后时序损失: {temporal_loss3.item():.6f}")

    print(f"\n第一层任务分配注意力机制测试完成!")
    return task_attention, state_processor

def test_collaboration_attention():
    """测试第二层协作感知注意力机制"""
    print("=" * 60)
    print("测试第二层协作感知注意力机制")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过协作注意力机制测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    task_attention = TaskAllocationAttention(device=device)
    collab_attention = CollaborationAttention(device=device)

    print(f"\n协作注意力机制配置:")
    print(f"  嵌入维度: {collab_attention.embed_dim}")
    print(f"  注意力头数: {collab_attention.num_heads}")
    print(f"  设备: {collab_attention.device}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 第一层注意力前向传播
    print(f"\n第一层注意力前向传播:")
    with torch.no_grad():
        first_output, first_attention, _ = task_attention(agv_embedded, task_embedded)

    print(f"  第一层输出形状: {first_output.shape}")
    print(f"  第一层注意力权重形状: {first_attention.shape}")

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)  # (batch_size, num_agvs, 3) [x, y, θ]
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x  # x坐标
        agv_positions[0, i, 1] = agv.y  # y坐标
        agv_positions[0, i, 2] = 0.0    # θ角度（简化为0）

    print(f"\nAGV位置信息:")
    for i in range(4):
        x, y, theta = agv_positions[0, i].cpu().numpy()
        print(f"  AGV {i}: 位置({x:.1f}, {y:.1f}), 角度{theta:.1f}")

    # 创建协作约束信息
    print(f"\n创建协作约束信息:")

    # 碰撞风险（基于距离）
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                # 距离越近，碰撞风险越高
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    # 路径冲突（简化为随机值）
    path_conflict = torch.rand(1, 4, 4, device=device) * 0.5

    # 负载不均衡（基于当前载重）
    loads = torch.tensor([agv.current_load for agv in env.agvs], device=device).float()
    load_imbalance = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                load_imbalance[0, i, j] = torch.abs(loads[i] - loads[j]) / 25.0  # 归一化

    # 协作历史（简化为随机值）
    collaboration_history = torch.rand(1, 4, 4, device=device) * 0.3

    constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': path_conflict.cpu().numpy(),
        'load_imbalance': load_imbalance.cpu().numpy(),
        'collaboration_history': collaboration_history.cpu().numpy()
    }

    print(f"  碰撞风险矩阵形状: {collision_risk.shape}")
    print(f"  路径冲突矩阵形状: {path_conflict.shape}")
    print(f"  负载不均衡矩阵形状: {load_imbalance.shape}")
    print(f"  协作历史矩阵形状: {collaboration_history.shape}")

    # 第二层协作注意力前向传播
    print(f"\n第二层协作注意力前向传播:")
    try:
        with torch.no_grad():
            collab_output, collab_weights = collab_attention(
                first_output, first_attention, agv_positions, constraints
            )

        print(f"  ✅ 前向传播成功")
        print(f"  协作输出形状: {collab_output.shape}")
        print(f"  协作注意力权重形状: {collab_weights.shape}")

        # 分析协作注意力权重
        print(f"\n协作注意力权重分析:")
        print(f"  协作权重原始形状: {collab_weights.shape}")

        # 处理权重形状问题
        if len(collab_weights.shape) == 4:
            # 如果是4维，取第一个batch和最后一个维度的平均
            collab_weights_np = collab_weights.cpu().numpy()[0].mean(axis=-1)  # (4, 4)
        else:
            collab_weights_np = collab_weights.cpu().numpy()[0]  # (4, 4)

        print(f"  处理后权重形状: {collab_weights_np.shape}")

        for i in range(4):
            agv_collab = collab_weights_np[i]
            # 找到最关注的其他AGV（排除自己）
            other_agvs = [(j, float(agv_collab[j])) for j in range(4) if j != i]
            other_agvs.sort(key=lambda x: x[1], reverse=True)

            top_collaborators = other_agvs[:2]  # 前2个协作伙伴
            print(f"  AGV {i} 最关注的协作伙伴: {[f'AGV{j}({w:.3f})' for j, w in top_collaborators]}")

        # 分析层次化注意力效果
        print(f"\n层次化注意力分析:")
        # 计算不同距离范围内的AGV对数量
        distances = torch.zeros(4, 4)
        for i in range(4):
            for j in range(4):
                if i != j:
                    dist = torch.sqrt(
                        (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                        (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                    )
                    distances[i, j] = dist

        near_pairs = (distances < 3).sum().item()
        mid_pairs = ((distances >= 3) & (distances < 8)).sum().item()
        far_pairs = (distances >= 8).sum().item()

        print(f"  近距离协作对数 (< 3): {near_pairs}")
        print(f"  中距离协作对数 (3-8): {mid_pairs}")
        print(f"  远距离协作对数 (≥ 8): {far_pairs}")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n第二层协作感知注意力机制测试完成!")
    return collab_attention

def test_dual_attention_fusion():
    """测试双层注意力融合机制"""
    print("=" * 60)
    print("测试双层注意力融合机制")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过双层注意力融合测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    integrated_attention = IntegratedDualAttention(device=device)

    print(f"\n集成双层注意力系统配置:")
    print(f"  嵌入维度: {integrated_attention.embed_dim}")
    print(f"  设备: {integrated_attention.device}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    # 创建约束信息
    print(f"\n创建约束信息:")

    # 任务分配约束
    distances = np.zeros((1, 4, 16))
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])
    priorities = np.random.uniform(0.5, 1.5, (1, 16))
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    task_constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    # 协作约束
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    collaboration_constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.5,
        'load_imbalance': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3,
        'collaboration_history': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3
    }

    print(f"  任务约束创建完成")
    print(f"  协作约束创建完成")

    # 集成双层注意力前向传播
    print(f"\n集成双层注意力前向传播:")
    try:
        with torch.no_grad():
            results = integrated_attention(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints
            )

        print(f"  ✅ 前向传播成功")
        print(f"  最终输出形状: {results['final_output'].shape}")
        print(f"  任务注意力输出形状: {results['task_attention_output'].shape}")
        print(f"  协作输出形状: {results['collaboration_output'].shape}")
        print(f"  门控值形状: {results['gate_values'].shape}")

        # 分析门控融合效果
        print(f"\n门控融合分析:")
        gate_values_np = results['gate_values'].cpu().numpy()[0]  # (4, 64)

        for i in range(4):
            agv_gates = gate_values_np[i]
            mean_gate = agv_gates.mean()
            task_dominance = (agv_gates > 0.5).mean()

            print(f"  AGV {i}: 平均门控值{mean_gate:.3f}, 任务注意力主导比例{task_dominance:.3f}")

        # 获取综合统计信息
        print(f"\n综合统计信息:")
        stats = integrated_attention.get_comprehensive_statistics(results)

        print(f"  融合统计:")
        for key, value in stats['fusion'].items():
            print(f"    {key}: {value:.4f}")

        print(f"  任务注意力统计:")
        for key, value in stats['task_attention'].items():
            print(f"    {key}: {value:.4f}")

        print(f"  协作注意力统计:")
        for key, value in stats['collaboration'].items():
            print(f"    {key}: {value:.4f}")

        print(f"  时序损失: {stats['temporal_loss']:.6f}")

        # 测试时序一致性
        print(f"\n时序一致性测试:")
        with torch.no_grad():
            results2 = integrated_attention(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints
            )

        temporal_loss2 = results2['temporal_loss'].item()
        print(f"  第二次前向传播时序损失: {temporal_loss2:.6f}")

        # 计算输出变化
        output_diff = torch.abs(results['final_output'] - results2['final_output']).mean().item()
        print(f"  最终输出平均变化: {output_diff:.6f}")

        # 重置时序状态测试
        print(f"\n重置时序状态测试:")
        integrated_attention.reset_temporal_states()

        with torch.no_grad():
            results3 = integrated_attention(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints
            )

        temporal_loss3 = results3['temporal_loss'].item()
        print(f"  重置后时序损失: {temporal_loss3:.6f}")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n双层注意力融合机制测试完成!")
    return integrated_attention

def test_attention_enhanced_policy():
    """测试注意力增强策略网络"""
    print("=" * 60)
    print("测试注意力增强策略网络")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过策略网络测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    policy_network = AttentionEnhancedPolicyNetwork(
        embed_dim=64,
        num_heads=8,
        top_k=8,
        high_level_actions=16,  # 16个任务选择
        low_level_actions=5,    # 5个运动控制
        device=device
    )

    print(f"\n策略网络配置:")
    print(f"  嵌入维度: {policy_network.embed_dim}")
    print(f"  注意力头数: {policy_network.num_heads}")
    print(f"  高层动作数: {policy_network.high_level_actions}")
    print(f"  低层动作数: {policy_network.low_level_actions}")
    print(f"  设备: {policy_network.device}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    # 创建约束信息
    print(f"\n创建约束信息:")

    # 任务分配约束
    distances = np.zeros((1, 4, 16))
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])
    priorities = np.random.uniform(0.5, 1.5, (1, 16))
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    task_constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    # 协作约束
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    collaboration_constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.5,
        'load_imbalance': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3,
        'collaboration_history': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3
    }

    print(f"  任务约束创建完成")
    print(f"  协作约束创建完成")

    # 生成动作掩码
    env_state = {
        'task_available': task_available[0],
        'collision_risk': collision_risk.cpu().numpy()[0]
    }
    action_masks = policy_network.get_action_masks(agv_embedded, task_embedded, env_state)

    print(f"\n动作掩码生成:")
    print(f"  动作掩码形状: {action_masks.shape}")
    print(f"  高层动作掩码均值: {action_masks[0, :, :16].mean().item():.3f}")
    print(f"  低层动作掩码均值: {action_masks[0, :, 16:].mean().item():.3f}")

    # 策略网络前向传播
    print(f"\n策略网络前向传播:")
    try:
        with torch.no_grad():
            policy_output = policy_network(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints, action_masks
            )

        print(f"  ✅ 前向传播成功")
        print(f"  高层logits形状: {policy_output['high_level_logits'].shape}")
        print(f"  低层logits形状: {policy_output['low_level_logits'].shape}")
        print(f"  高层概率形状: {policy_output['high_level_probs'].shape}")
        print(f"  低层概率形状: {policy_output['low_level_probs'].shape}")
        print(f"  融合特征形状: {policy_output['fused_features'].shape}")

        # 动作采样测试
        print(f"\n动作采样测试:")
        high_actions, low_actions = policy_network.sample_actions(policy_output)
        print(f"  高层动作形状: {high_actions.shape}")
        print(f"  低层动作形状: {low_actions.shape}")

        # 显示采样的动作
        for i in range(4):
            high_action = high_actions[0, i].item()
            low_action = low_actions[0, i].item()
            print(f"  AGV {i}: 任务选择={high_action}, 运动控制={low_action}")

        # 动作评估测试
        print(f"\n动作评估测试:")
        action_eval = policy_network.evaluate_actions(policy_output, high_actions, low_actions)

        print(f"  高层log概率形状: {action_eval['high_level_log_probs'].shape}")
        print(f"  低层log概率形状: {action_eval['low_level_log_probs'].shape}")
        print(f"  总log概率形状: {action_eval['total_log_probs'].shape}")
        print(f"  总熵形状: {action_eval['total_entropy'].shape}")

        # 分析概率分布
        print(f"\n概率分布分析:")
        high_probs_np = policy_output['high_level_probs'].cpu().numpy()[0]
        low_probs_np = policy_output['low_level_probs'].cpu().numpy()[0]

        for i in range(4):
            high_entropy = -np.sum(high_probs_np[i] * np.log(high_probs_np[i] + 1e-8))
            low_entropy = -np.sum(low_probs_np[i] * np.log(low_probs_np[i] + 1e-8))

            print(f"  AGV {i}: 高层熵={high_entropy:.3f}, 低层熵={low_entropy:.3f}")

        # 掩码效果分析
        print(f"\n掩码效果分析:")
        high_masks_np = policy_output['high_level_masks'].cpu().numpy()[0]
        low_masks_np = policy_output['low_level_masks'].cpu().numpy()[0]

        for i in range(4):
            high_valid = (high_masks_np[i] > 0.1).sum()
            low_valid = (low_masks_np[i] > 0.1).sum()

            print(f"  AGV {i}: 有效高层动作={high_valid}/16, 有效低层动作={low_valid}/5")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n注意力增强策略网络测试完成!")
    return policy_network

def test_attention_enhanced_value():
    """测试注意力增强价值网络"""
    print("=" * 60)
    print("测试注意力增强价值网络")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过价值网络测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    value_network = AttentionEnhancedValueNetwork(
        embed_dim=64,
        num_heads=8,
        top_k=8,
        device=device
    )

    print(f"\n价值网络配置:")
    print(f"  嵌入维度: {value_network.embed_dim}")
    print(f"  注意力头数: {value_network.num_heads}")
    print(f"  设备: {value_network.device}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    # 创建约束信息
    print(f"\n创建约束信息:")

    # 任务分配约束
    distances = np.zeros((1, 4, 16))
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])
    priorities = np.random.uniform(0.5, 1.5, (1, 16))
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    task_constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    # 协作约束
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    collaboration_constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.5,
        'load_imbalance': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3,
        'collaboration_history': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3
    }

    print(f"  任务约束创建完成")
    print(f"  协作约束创建完成")

    # 创建模拟的策略特征（来自策略网络）
    policy_features = torch.randn(1, 4, 64, device=device)

    # 价值网络前向传播
    print(f"\n价值网络前向传播:")
    try:
        with torch.no_grad():
            value_output = value_network(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints, policy_features
            )

        print(f"  ✅ 前向传播成功")
        print(f"  个体价值形状: {value_output['individual_values'].shape}")
        print(f"  全局价值形状: {value_output['global_value'].shape}")
        print(f"  融合价值形状: {value_output['fused_values'].shape}")
        print(f"  一致性分数形状: {value_output['consistency_scores'].shape}")
        print(f"  全局注意力权重形状: {value_output['global_attention_weights'].shape}")

        # 价值分析
        print(f"\n价值分析:")
        individual_values = value_output['individual_values'].cpu().numpy()[0]
        global_value = value_output['global_value'].cpu().numpy()[0]
        fused_values = value_output['fused_values'].cpu().numpy()[0]
        consistency_scores = value_output['consistency_scores'].cpu().numpy()[0]

        print(f"  个体价值: {[f'{v:.3f}' for v in individual_values]}")
        print(f"  全局价值: {global_value:.3f}")
        print(f"  融合价值: {[f'{v:.3f}' for v in fused_values]}")
        print(f"  一致性分数: {[f'{s:.3f}' for s in consistency_scores]}")

        # 全局注意力权重分析
        print(f"\n全局注意力权重分析:")
        global_attention = value_output['global_attention_weights'].cpu().numpy()[0]

        for i in range(4):
            agv_attention = global_attention[i]
            # 找到最关注的其他AGV
            other_agvs = [(j, agv_attention[j]) for j in range(4) if j != i]
            other_agvs.sort(key=lambda x: x[1], reverse=True)

            top_collaborators = other_agvs[:2]
            print(f"  AGV {i} 最关注的AGV: {[f'AGV{j}({w:.3f})' for j, w in top_collaborators]}")

        # 获取统计信息
        print(f"\n价值网络统计信息:")
        stats = value_network.get_value_statistics(value_output)

        print(f"  个体价值统计:")
        print(f"    均值: {stats['individual_value_mean']:.4f}")
        print(f"    标准差: {stats['individual_value_std']:.4f}")
        print(f"    范围: [{stats['individual_value_min']:.4f}, {stats['individual_value_max']:.4f}]")

        print(f"  全局价值统计:")
        print(f"    均值: {stats['global_value_mean']:.4f}")
        print(f"    标准差: {stats['global_value_std']:.4f}")

        print(f"  融合价值统计:")
        print(f"    均值: {stats['fused_value_mean']:.4f}")
        print(f"    标准差: {stats['fused_value_std']:.4f}")

        print(f"  一致性统计:")
        print(f"    均值: {stats['consistency_mean']:.4f}")
        print(f"    标准差: {stats['consistency_std']:.4f}")

        print(f"  全局注意力统计:")
        print(f"    均值: {stats['global_attention_mean']:.4f}")
        print(f"    熵: {stats['global_attention_entropy']:.4f}")

        # 损失计算测试
        print(f"\n损失计算测试:")

        # 创建模拟目标值
        target_individual = torch.randn_like(value_output['individual_values'])
        target_global = torch.randn_like(value_output['global_value'])
        target_fused = torch.randn_like(value_output['fused_values'])

        individual_loss = value_network.compute_value_loss(
            value_output['individual_values'], target_individual, 'individual'
        )
        global_loss = value_network.compute_value_loss(
            value_output['global_value'], target_global, 'global'
        )
        fused_loss = value_network.compute_value_loss(
            value_output['fused_values'], target_fused, 'fused'
        )
        consistency_loss = value_network.compute_attention_consistency_loss(
            value_output['consistency_scores']
        )

        print(f"  个体价值损失: {individual_loss.item():.6f}")
        print(f"  全局价值损失: {global_loss.item():.6f}")
        print(f"  融合价值损失: {fused_loss.item():.6f}")
        print(f"  一致性损失: {consistency_loss.item():.6f}")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n注意力增强价值网络测试完成!")
    return value_network

def test_mappo_with_dual_attention():
    """测试集成双层注意力的MAPPO系统"""
    print("=" * 60)
    print("测试集成双层注意力的MAPPO系统")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过MAPPO系统测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    mappo_system = MAPPOWithDualAttention(
        embed_dim=64,
        num_heads=8,
        top_k=8,
        high_level_actions=16,
        low_level_actions=5,
        device=device
    )

    print(f"\nMAPPO系统配置:")
    print(f"  嵌入维度: {mappo_system.embed_dim}")
    print(f"  设备: {mappo_system.device}")
    print(f"  策略网络参数数量: {sum(p.numel() for p in mappo_system.policy_network.parameters())}")
    print(f"  价值网络参数数量: {sum(p.numel() for p in mappo_system.value_network.parameters())}")
    print(f"  总参数数量: {sum(p.numel() for p in mappo_system.parameters())}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    # 创建约束信息
    print(f"\n创建约束信息:")

    # 任务分配约束
    distances = np.zeros((1, 4, 16))
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])
    priorities = np.random.uniform(0.5, 1.5, (1, 16))
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    task_constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    # 协作约束
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    collaboration_constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.5,
        'load_imbalance': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3,
        'collaboration_history': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3
    }

    # 生成动作掩码
    env_state = {
        'task_available': task_available[0],
        'collision_risk': collision_risk.cpu().numpy()[0]
    }
    action_masks = mappo_system.policy_network.get_action_masks(agv_embedded, task_embedded, env_state)

    print(f"  约束信息创建完成")
    print(f"  动作掩码生成完成")

    # MAPPO系统前向传播
    print(f"\nMAPPO系统前向传播:")
    try:
        with torch.no_grad():
            mappo_output = mappo_system(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints, action_masks
            )

        print(f"  ✅ 前向传播成功")

        # 策略输出分析
        print(f"\n策略输出分析:")
        print(f"  高层概率形状: {mappo_output['high_level_probs'].shape}")
        print(f"  低层概率形状: {mappo_output['low_level_probs'].shape}")
        print(f"  策略融合特征形状: {mappo_output['fused_features'].shape}")

        # 价值输出分析
        print(f"\n价值输出分析:")
        print(f"  个体价值形状: {mappo_output['individual_values'].shape}")
        print(f"  全局价值形状: {mappo_output['global_value'].shape}")
        print(f"  融合价值形状: {mappo_output['fused_values'].shape}")
        print(f"  一致性分数形状: {mappo_output['consistency_scores'].shape}")

        # 动作采样和评估
        print(f"\n动作采样和评估:")
        high_actions, low_actions = mappo_system.policy_network.sample_actions(mappo_output)
        action_eval = mappo_system.policy_network.evaluate_actions(mappo_output, high_actions, low_actions)

        print(f"  采样动作形状: 高层{high_actions.shape}, 低层{low_actions.shape}")
        print(f"  动作评估: log概率{action_eval['total_log_probs'].shape}, 熵{action_eval['total_entropy'].shape}")

        # 显示具体数值
        print(f"\n具体数值分析:")
        individual_values = mappo_output['individual_values'].cpu().numpy()[0]
        global_value = mappo_output['global_value'].cpu().numpy()[0]
        fused_values = mappo_output['fused_values'].cpu().numpy()[0]

        print(f"  个体价值: {[f'{v:.3f}' for v in individual_values]}")
        print(f"  全局价值: {global_value:.3f}")
        print(f"  融合价值: {[f'{v:.3f}' for v in fused_values]}")

        # 采样的动作
        high_actions_np = high_actions.cpu().numpy()[0]
        low_actions_np = low_actions.cpu().numpy()[0]

        for i in range(4):
            print(f"  AGV {i}: 任务选择={high_actions_np[i]}, 运动控制={low_actions_np[i]}")

        # 计算总损失（模拟）
        print(f"\n损失计算测试:")

        # 模拟目标值
        target_individual = torch.randn_like(mappo_output['individual_values'])
        target_global = torch.randn_like(mappo_output['global_value'])
        target_fused = torch.randn_like(mappo_output['fused_values'])

        # 价值损失
        individual_loss = mappo_system.value_network.compute_value_loss(
            mappo_output['individual_values'], target_individual, 'individual'
        )
        global_loss = mappo_system.value_network.compute_value_loss(
            mappo_output['global_value'], target_global, 'global'
        )
        fused_loss = mappo_system.value_network.compute_value_loss(
            mappo_output['fused_values'], target_fused, 'fused'
        )
        consistency_loss = mappo_system.value_network.compute_attention_consistency_loss(
            mappo_output['consistency_scores']
        )

        total_value_loss = individual_loss + global_loss + fused_loss + 0.1 * consistency_loss

        print(f"  个体价值损失: {individual_loss.item():.6f}")
        print(f"  全局价值损失: {global_loss.item():.6f}")
        print(f"  融合价值损失: {fused_loss.item():.6f}")
        print(f"  一致性损失: {consistency_loss.item():.6f}")
        print(f"  总价值损失: {total_value_loss.item():.6f}")

        # 策略损失（简化版PPO损失）
        log_probs = action_eval['total_log_probs']
        entropy = action_eval['total_entropy']

        # 模拟优势函数
        advantages = torch.randn_like(log_probs)
        old_log_probs = torch.randn_like(log_probs)

        # PPO损失计算
        ratio = torch.exp(log_probs - old_log_probs)
        clip_ratio = torch.clamp(ratio, 0.8, 1.2)
        policy_loss = -torch.min(ratio * advantages, clip_ratio * advantages).mean()
        entropy_loss = -0.01 * entropy.mean()

        total_policy_loss = policy_loss + entropy_loss

        print(f"  策略损失: {policy_loss.item():.6f}")
        print(f"  熵损失: {entropy_loss.item():.6f}")
        print(f"  总策略损失: {total_policy_loss.item():.6f}")

        total_loss = total_policy_loss + total_value_loss
        print(f"  系统总损失: {total_loss.item():.6f}")

    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n集成双层注意力的MAPPO系统测试完成!")
    return mappo_system

def test_mappo_loss_function():
    """测试MAPPO训练目标函数"""
    print("=" * 60)
    print("测试MAPPO训练目标函数")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过损失函数测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建环境和组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    mappo_system = MAPPOWithDualAttention(device=device)
    loss_function = MAPPOLossFunction(device=device)
    trainer = MAPPOTrainer(mappo_system, loss_function, device=device)

    print(f"\n损失函数配置:")
    print(f"  裁剪比率: {loss_function.clip_ratio}")
    print(f"  熵系数: {loss_function.entropy_coef}")
    print(f"  价值系数: {loss_function.value_coef}")
    print(f"  注意力正则化系数: {loss_function.attention_reg_coef}")
    print(f"  时序一致性系数: {loss_function.temporal_consistency_coef}")
    print(f"  一致性系数: {loss_function.consistency_coef}")

    # 重置环境并获取观察
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)    # (1, 4, 64)
    task_embedded = global_embedded[4:].unsqueeze(0)   # (1, 16, 64)

    # 创建AGV位置信息
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    # 创建约束信息
    print(f"\n创建训练数据:")

    # 任务分配约束
    distances = np.zeros((1, 4, 16))
    capacity_valid = np.zeros((1, 4, 16), dtype=bool)
    for i, agv in enumerate(env.agvs):
        for j, task in enumerate(env.tasks):
            distances[0, i, j] = abs(agv.x - task.x) + abs(agv.y - task.y)
            capacity_valid[0, i, j] = agv.can_take_task(task.weight)

    task_available = np.array([[task.status == TaskStatus.UNASSIGNED for task in env.tasks]])
    priorities = np.random.uniform(0.5, 1.5, (1, 16))
    time_urgency = np.random.uniform(0.0, 1.0, (1, 16))

    task_constraints = {
        'distances': distances,
        'capacity_valid': capacity_valid,
        'task_available': task_available,
        'priorities': priorities,
        'time_urgency': time_urgency
    }

    # 协作约束
    collision_risk = torch.zeros(1, 4, 4, device=device)
    for i in range(4):
        for j in range(4):
            if i != j:
                dist = torch.sqrt(
                    (agv_positions[0, i, 0] - agv_positions[0, j, 0]) ** 2 +
                    (agv_positions[0, i, 1] - agv_positions[0, j, 1]) ** 2
                )
                collision_risk[0, i, j] = torch.exp(-dist / 2.0)

    collaboration_constraints = {
        'collision_risk': collision_risk.cpu().numpy(),
        'path_conflict': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.5,
        'load_imbalance': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3,
        'collaboration_history': torch.rand(1, 4, 4, device=device).cpu().numpy() * 0.3
    }

    # 生成动作掩码
    env_state = {
        'task_available': task_available[0],
        'collision_risk': collision_risk.cpu().numpy()[0]
    }
    action_masks = mappo_system.policy_network.get_action_masks(agv_embedded, task_embedded, env_state)

    print(f"  约束信息创建完成")
    print(f"  动作掩码生成完成")

    # 获取当前网络输出
    print(f"\n获取网络输出:")
    with torch.no_grad():
        current_output = mappo_system(
            agv_embedded, task_embedded, agv_positions,
            task_constraints, collaboration_constraints, action_masks
        )

    print(f"  网络前向传播成功")

    # 创建模拟训练数据
    print(f"\n创建模拟训练数据:")

    # 模拟旧的log概率和价值
    old_log_probs = torch.randn(1, 4, device=device) * 0.1
    old_values = torch.randn(1, 4, device=device) * 2.0

    # 模拟奖励和下一步价值
    rewards = torch.randn(1, 4, device=device) * 1.0
    next_values = torch.randn(1, 4, device=device) * 2.0
    dones = torch.zeros(1, 4, device=device)

    # 模拟旧的注意力权重
    old_attention_weights = torch.rand(1, 4, 16, device=device)
    old_attention_weights = old_attention_weights / old_attention_weights.sum(dim=-1, keepdim=True)

    print(f"  模拟数据创建完成")

    # 计算优势函数
    print(f"\n计算优势函数:")
    advantages = trainer.compute_advantages(rewards, old_values, next_values, dones)
    print(f"  优势函数形状: {advantages.shape}")
    print(f"  优势函数统计: 均值={advantages.mean().item():.4f}, 标准差={advantages.std().item():.4f}")

    # 计算策略损失
    print(f"\n计算策略损失:")
    try:
        policy_losses = loss_function.compute_policy_loss(
            current_output, old_log_probs, advantages, old_attention_weights
        )

        print(f"  ✅ 策略损失计算成功")
        print(f"  PPO损失: {policy_losses['ppo_loss'].item():.6f}")
        print(f"  熵损失: {policy_losses['entropy_loss'].item():.6f}")
        print(f"  注意力正则化损失: {policy_losses['attention_reg_loss'].item():.6f}")
        print(f"  时序一致性损失: {policy_losses['temporal_loss'].item():.6f}")
        print(f"  总策略损失: {policy_losses['total_policy_loss'].item():.6f}")

        print(f"  重要性采样比率: 均值={policy_losses['ratio_mean'].item():.4f}, 标准差={policy_losses['ratio_std'].item():.4f}")
        print(f"  优势函数: 均值={policy_losses['advantages_mean'].item():.4f}, 标准差={policy_losses['advantages_std'].item():.4f}")

    except Exception as e:
        print(f"  ❌ 策略损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    # 计算价值损失
    print(f"\n计算价值损失:")
    try:
        target_values = advantages + old_values
        value_losses = loss_function.compute_value_loss(current_output, target_values, old_values)

        print(f"  ✅ 价值损失计算成功")
        print(f"  个体价值损失: {value_losses['individual_value_loss'].item():.6f}")
        print(f"  全局价值损失: {value_losses['global_value_loss'].item():.6f}")
        print(f"  融合价值损失: {value_losses['fused_value_loss'].item():.6f}")
        print(f"  价值裁剪损失: {value_losses['value_clip_loss'].item():.6f}")
        print(f"  一致性损失: {value_losses['consistency_loss'].item():.6f}")
        print(f"  价值一致性损失: {value_losses['value_consistency_loss'].item():.6f}")
        print(f"  总价值损失: {value_losses['total_value_loss'].item():.6f}")

    except Exception as e:
        print(f"  ❌ 价值损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    # 计算总损失
    print(f"\n计算总损失:")
    try:
        total_losses = loss_function.compute_total_loss(policy_losses, value_losses)

        print(f"  ✅ 总损失计算成功")
        print(f"  系统总损失: {total_losses['total_loss'].item():.6f}")
        print(f"  策略损失权重: 1.0")
        print(f"  价值损失权重: {loss_function.value_coef}")

        # 损失统计
        loss_stats = loss_function.get_loss_statistics(total_losses)
        print(f"\n损失统计信息:")
        for key, value in loss_stats.items():
            if 'loss' in key:
                print(f"    {key}: {value:.6f}")

    except Exception as e:
        print(f"  ❌ 总损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    # 测试训练步骤（不执行反向传播）
    print(f"\n测试训练步骤:")
    try:
        # 创建批次数据
        batch_data = {
            'agv_features': agv_embedded,
            'task_features': task_embedded,
            'agv_positions': agv_positions,
            'task_constraints': task_constraints,
            'collaboration_constraints': collaboration_constraints,
            'action_masks': action_masks,
            'old_log_probs': old_log_probs,
            'old_values': old_values,
            'rewards': rewards,
            'next_values': next_values,
            'dones': dones,
            'old_attention_weights': old_attention_weights
        }

        # 模拟训练步骤（不实际更新参数）
        with torch.no_grad():
            # 重新计算以模拟训练步骤
            current_output_train = mappo_system(
                agv_embedded, task_embedded, agv_positions,
                task_constraints, collaboration_constraints, action_masks
            )

            advantages_train = trainer.compute_advantages(rewards, old_values, next_values, dones)
            advantages_train = (advantages_train - advantages_train.mean()) / (advantages_train.std() + 1e-8)
            target_values_train = advantages_train + old_values

            policy_losses_train = loss_function.compute_policy_loss(
                current_output_train, old_log_probs, advantages_train, old_attention_weights
            )

            value_losses_train = loss_function.compute_value_loss(
                current_output_train, target_values_train, old_values
            )

            total_losses_train = loss_function.compute_total_loss(policy_losses_train, value_losses_train)

        print(f"  ✅ 训练步骤模拟成功")
        print(f"  模拟总损失: {total_losses_train['total_loss'].item():.6f}")
        print(f"  训练器状态: 总步数={trainer.training_stats['total_steps']}")

    except Exception as e:
        print(f"  ❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\nMAPPO训练目标函数测试完成!")
    return loss_function, trainer

def test_training_optimization():
    """测试训练策略与优化算法"""
    print("=" * 60)
    print("测试训练策略与优化算法")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过训练优化测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    mappo_system = MAPPOWithDualAttention(device=device)

    # 创建训练优化组件
    experience_replay = PrioritizedExperienceReplay(
        capacity=1000, alpha=0.6, beta=0.4, device=device
    )
    curriculum_learning = AdaptiveCurriculumLearning(device=device)
    stability_manager = TrainingStabilityManager(device=device)

    print(f"\n训练优化组件配置:")
    print(f"  经验回放容量: {experience_replay.capacity}")
    print(f"  课程学习初始难度: {curriculum_learning.current_difficulty}")
    print(f"  稳定性管理梯度裁剪: {stability_manager.gradient_clip_norm}")

    # 模拟训练数据生成
    print(f"\n生成模拟训练数据:")

    # 重置环境
    obs = env.reset()
    global_state = obs['global_state']

    # 处理状态
    global_embedded = state_processor.process_global_state(global_state)
    agv_embedded = global_embedded[:4].unsqueeze(0)
    task_embedded = global_embedded[4:].unsqueeze(0)

    # 创建AGV位置
    agv_positions = torch.zeros(1, 4, 3, device=device)
    for i, agv in enumerate(env.agvs):
        agv_positions[0, i, 0] = agv.x
        agv_positions[0, i, 1] = agv.y
        agv_positions[0, i, 2] = 0.0

    print(f"  状态处理完成")

    # 测试优先级经验回放
    print(f"\n测试优先级经验回放:")

    # 创建模拟经验
    for i in range(20):
        # 模拟经验数据
        actions = {
            'high_level': torch.randint(0, 16, (1, 4), device=device),
            'low_level': torch.randint(0, 5, (1, 4), device=device)
        }
        rewards = torch.randn(1, 4, device=device)
        log_probs = torch.randn(1, 4, device=device) * 0.1
        values = torch.randn(1, 4, device=device) * 2.0
        dones = torch.zeros(1, 4, device=device)

        # 模拟注意力权重
        attention_weights = {
            'task_attention': torch.rand(1, 4, 16, device=device),
            'collaboration': torch.rand(1, 4, 4, device=device)
        }
        attention_weights['task_attention'] = attention_weights['task_attention'] / attention_weights['task_attention'].sum(dim=-1, keepdim=True)
        attention_weights['collaboration'] = attention_weights['collaboration'] / attention_weights['collaboration'].sum(dim=-1, keepdim=True)

        # 创建经验
        experience = MultiAgentExperience(
            agv_features=agv_embedded.clone(),
            task_features=task_embedded.clone(),
            agv_positions=agv_positions.clone(),
            actions=actions,
            rewards=rewards,
            next_agv_features=agv_embedded.clone(),
            next_task_features=task_embedded.clone(),
            next_agv_positions=agv_positions.clone(),
            dones=dones,
            log_probs=log_probs,
            values=values,
            attention_weights=attention_weights,
            constraints={},
            timestamp=float(i)
        )

        # 添加到经验回放
        experience_replay.add(experience)
    print(f"  缓冲区大小: {experience_replay.size}")

    # 测试经验采样
    if experience_replay.size >= 8:
        experiences, indices, weights = experience_replay.sample(8)
        print(f"  ✅ 采样了8个经验")
        print(f"  采样索引: {indices}")
        print(f"  重要性权重: {weights[:4]}")  # 显示前4个权重

        # 测试优先级更新
        td_errors = np.random.randn(8) * 0.5
        attention_changes = np.random.rand(8) * 0.3
        experience_replay.update_priorities(indices, td_errors, attention_changes)
        print(f"  ✅ 更新了经验优先级")

    # 获取经验回放统计
    replay_stats = experience_replay.get_statistics()
    print(f"  经验回放统计:")
    print(f"    平均优先级: {replay_stats['avg_priority']:.4f}")
    print(f"    平均复杂度: {replay_stats.get('avg_complexity', 0):.4f}")
    print(f"    缓冲区利用率: {replay_stats['buffer_utilization']:.2%}")

    # 测试自适应课程学习
    print(f"\n测试自适应课程学习:")

    # 模拟训练过程
    for episode in range(30):
        # 模拟episode结果
        episode_reward = np.random.normal(0, 2)  # 随机奖励
        episode_success = episode_reward > 0     # 简单的成功判断
        attention_quality = np.random.uniform(0.3, 0.9)  # 注意力质量

        # 更新课程学习
        curriculum_learning.update_performance(episode_reward, episode_success, attention_quality)

    print(f"  ✅ 模拟了30个episode的课程学习")

    # 获取当前课程配置
    current_curriculum = curriculum_learning.get_current_curriculum()
    print(f"  当前课程配置:")
    print(f"    任务数量: {current_curriculum['num_tasks']}")
    print(f"    AGV数量: {current_curriculum['agv_count']}")
    print(f"    地图复杂度: {current_curriculum['map_complexity']:.2f}")
    print(f"    协作需求度: {current_curriculum['collaboration_requirement']:.2f}")

    # 获取注意力课程权重
    attention_weights = curriculum_learning.get_attention_curriculum_weights()
    print(f"  注意力课程权重:")
    for key, value in attention_weights.items():
        print(f"    {key}: {value:.3f}")

    # 获取课程学习统计
    curriculum_stats = curriculum_learning.get_statistics()
    print(f"  课程学习统计:")
    print(f"    当前难度: {curriculum_stats['current_difficulty']:.3f}")
    print(f"    成功率: {curriculum_stats['current_success_rate']:.3f}")
    print(f"    平均性能: {curriculum_stats['avg_performance']:.3f}")
    print(f"    难度调整次数: {curriculum_stats['total_adaptations']}")

    # 测试训练稳定性管理
    print(f"\n测试训练稳定性管理:")

    # 模拟训练步骤中的梯度检查
    for step in range(10):
        # 创建模拟损失
        loss = torch.tensor(np.random.exponential(1.0), device=device, requires_grad=True)

        # 模拟反向传播
        loss.backward()

        # 检查梯度稳定性
        gradient_stats = stability_manager.check_and_fix_gradients(mappo_system)

        # 检查损失稳定性
        loss_stable = stability_manager.check_loss_stability(loss)

        # 更新步数
        stability_manager.update_step()

        # 清除梯度
        mappo_system.zero_grad()

    print(f"  ✅ 模拟了10个训练步骤的稳定性检查")

    # 获取稳定性统计
    stability_stats = stability_manager.get_statistics()
    print(f"  稳定性统计:")
    print(f"    当前稳定性分数: {stability_stats['current_stability_score']:.3f}")
    print(f"    自适应学习率因子: {stability_stats['adaptive_lr_factor']:.3f}")
    print(f"    自适应裁剪范数: {stability_stats['adaptive_clip_norm']:.3f}")
    print(f"    梯度爆炸次数: {stability_stats['gradient_explosions']}")
    print(f"    NaN检测次数: {stability_stats['total_nan_detections']}")

    # 获取自适应正则化权重
    reg_weights = stability_manager.get_regularization_weights()
    print(f"  自适应正则化权重:")
    for key, value in reg_weights.items():
        print(f"    {key}: {value:.4f}")

    # 测试集成训练流程
    print(f"\n测试集成训练流程:")

    try:
        # 获取当前课程配置
        curriculum = curriculum_learning.get_current_curriculum()

        # 检查是否使用高级特性
        advanced_features = curriculum_learning.should_use_advanced_features()
        print(f"  高级特性使用情况:")
        for feature, use in advanced_features.items():
            print(f"    {feature}: {'启用' if use else '禁用'}")

        # 获取自适应学习率
        base_lr = 3e-4
        adaptive_lr = stability_manager.get_adaptive_learning_rate(base_lr)
        print(f"  学习率调整: {base_lr:.6f} -> {adaptive_lr:.6f}")

        # 检查是否应该跳过更新
        should_skip = stability_manager.should_skip_update()
        print(f"  是否跳过更新: {'是' if should_skip else '否'}")

        print(f"  ✅ 集成训练流程测试成功")

    except Exception as e:
        print(f"  ❌ 集成训练流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"\n训练策略与优化算法测试完成!")
    return {
        'experience_replay': experience_replay,
        'curriculum_learning': curriculum_learning,
        'stability_manager': stability_manager
    }

def test_enhanced_training_optimization():
    """测试增强的训练策略与优化算法"""
    print("=" * 60)
    print("测试增强的训练策略与优化算法")
    print("=" * 60)

    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过增强训练优化测试")
        return None

    # 检查GPU可用性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建组件
    env = WarehouseEnvironment()
    state_processor = StateProcessor(device=device)
    mappo_system = MAPPOWithDualAttention(device=device)

    # 创建增强的训练优化组件
    curriculum_learning = AdaptiveCurriculumLearning(device=device)
    stability_manager = TrainingStabilityManager(device=device)

    print(f"\n增强训练优化组件配置:")
    print(f"  课程学习当前难度: {curriculum_learning.current_difficulty}")
    print(f"  稳定性管理器初始化完成")

    # 测试多维度难度评估
    print(f"\n测试多维度难度评估:")
    multi_dim_difficulty = curriculum_learning.get_multi_dimensional_difficulty()

    print(f"  任务复杂度维度:")
    for key, value in multi_dim_difficulty['task_complexity'].items():
        print(f"    {key}: {value:.3f}")

    print(f"  协作复杂度维度:")
    for key, value in multi_dim_difficulty['collaboration_complexity'].items():
        print(f"    {key}: {value:.3f}")

    print(f"  环境复杂度维度:")
    for key, value in multi_dim_difficulty['environment_complexity'].items():
        print(f"    {key}: {value:.3f}")

    print(f"  注意力复杂度维度:")
    for key, value in multi_dim_difficulty['attention_complexity'].items():
        print(f"    {key}: {value:.3f}")

    # 测试技能分解课程学习
    print(f"\n测试技能分解课程学习:")
    skill_curriculum = curriculum_learning.get_skill_decomposition_curriculum()

    print(f"  基础技能权重:")
    for skill, weight in skill_curriculum['basic_skills'].items():
        print(f"    {skill}: {weight:.3f}")

    print(f"  中级技能权重:")
    for skill, weight in skill_curriculum['intermediate_skills'].items():
        print(f"    {skill}: {weight:.3f}")

    print(f"  高级技能权重:")
    for skill, weight in skill_curriculum['advanced_skills'].items():
        print(f"    {skill}: {weight:.3f}")

    print(f"  专家技能权重:")
    for skill, weight in skill_curriculum['expert_skills'].items():
        print(f"    {skill}: {weight:.3f}")

    # 测试渐进学习时间表
    print(f"\n测试渐进学习时间表:")
    learning_schedule = curriculum_learning.get_progressive_learning_schedule()

    print(f"  当前学习阶段: {learning_schedule['current_stage']}")
    print(f"  阶段进度: {learning_schedule['stage_progress']:.2%}")
    print(f"  重点学习领域: {learning_schedule['focus_areas']}")
    print(f"  下一个里程碑: {learning_schedule['next_milestone']}")
    print(f"  预计所需episode数: {learning_schedule['estimated_episodes_to_next']}")
    print(f"  学习效率: {learning_schedule['learning_efficiency']:.3f}")

    # 测试分层学习率调度
    print(f"\n测试分层学习率调度:")
    base_lr = 3e-4
    hierarchical_lrs = stability_manager.get_hierarchical_learning_rates(base_lr)

    print(f"  基础学习率: {base_lr:.6f}")
    print(f"  分层学习率:")
    for module, lr in hierarchical_lrs.items():
        print(f"    {module}: {lr:.6f} (倍数: {lr/base_lr:.2f})")

    # 测试高级梯度裁剪
    print(f"\n测试高级梯度裁剪:")

    # 创建模拟梯度
    for param in mappo_system.parameters():
        if param.requires_grad:
            param.grad = torch.randn_like(param) * 0.1

    module_norms = {
        'attention': 0.3,
        'policy': 0.5,
        'value': 0.7,
        'embedding': 0.2
    }

    gradient_stats = stability_manager.apply_advanced_gradient_clipping(mappo_system, module_norms)

    print(f"  梯度裁剪统计:")
    for key, value in gradient_stats.items():
        if isinstance(value, bool):
            print(f"    {key}: {'是' if value else '否'}")
        else:
            print(f"    {key}: {value:.4f}")

    # 测试高级正则化配置
    print(f"\n测试高级正则化配置:")
    reg_config = stability_manager.get_advanced_regularization_config()

    print(f"  注意力正则化:")
    for key, value in reg_config['attention_regularization'].items():
        print(f"    {key}: {value:.6f}")

    print(f"  参数共享正则化:")
    for key, value in reg_config['parameter_sharing_regularization'].items():
        print(f"    {key}: {value:.6f}")

    print(f"  时序平滑正则化:")
    for key, value in reg_config['temporal_smoothing_regularization'].items():
        print(f"    {key}: {value:.6f}")

    # 测试训练异常检测
    print(f"\n测试训练异常检测:")

    # 模拟不同的训练指标
    test_scenarios = [
        {'grad_norm': 0.5, 'loss': 1.2, 'scenario': '正常训练'},
        {'grad_norm': 15.0, 'loss': 2.5, 'scenario': '梯度爆炸'},
        {'grad_norm': 1e-8, 'loss': 1.0, 'scenario': '梯度消失'},
        {'grad_norm': 0.8, 'loss': float('nan'), 'scenario': 'NaN损失'}
    ]

    for scenario in test_scenarios:
        print(f"  场景: {scenario['scenario']}")

        # 添加一些损失历史以便检测
        for _ in range(20):
            if scenario['scenario'] == 'NaN损失':
                stability_manager.loss_history.append(float('nan'))
            else:
                stability_manager.loss_history.append(scenario['loss'] + np.random.normal(0, 0.1))

        anomalies = stability_manager.detect_training_anomalies(scenario)

        print(f"    检测到的异常: {anomalies['detected_anomalies']}")
        print(f"    严重程度: {anomalies['severity_level']}")
        print(f"    推荐行动: {anomalies['recommended_actions']}")

        # 清理历史记录
        stability_manager.loss_history.clear()

    # 测试自适应训练调度
    print(f"\n测试自适应训练调度:")

    # 模拟不同稳定性水平
    stability_levels = [0.9, 0.7, 0.4]

    for stability in stability_levels:
        # 手动设置稳定性分数
        stability_manager.stats['current_stability_score'] = stability

        schedule = stability_manager.get_adaptive_training_schedule()

        print(f"  稳定性分数: {stability:.1f}")
        print(f"    学习率倍数: {schedule['learning_rate_multiplier']:.2f}")
        print(f"    批次大小倍数: {schedule['batch_size_multiplier']:.2f}")
        print(f"    更新频率: {schedule['update_frequency']:.2f}")
        print(f"    探索率: {schedule['exploration_rate']:.2f}")
        print(f"    正则化强度: {schedule['regularization_strength']:.2f}")
        print(f"    推荐检查点频率: {schedule['recommended_checkpoint_frequency']}")

    # 测试课程学习的动态调整
    print(f"\n测试课程学习动态调整:")

    # 模拟一系列训练episode
    print(f"  模拟训练过程:")
    for episode in range(50):
        # 模拟不同的性能表现
        if episode < 20:
            # 初期：性能较差
            episode_reward = np.random.normal(-1, 1)
            episode_success = episode_reward > -0.5
        elif episode < 35:
            # 中期：性能改善
            episode_reward = np.random.normal(1, 0.8)
            episode_success = episode_reward > 0
        else:
            # 后期：性能稳定
            episode_reward = np.random.normal(2, 0.5)
            episode_success = episode_reward > 1

        attention_quality = min(1.0, max(0.0, 0.3 + episode * 0.01 + np.random.normal(0, 0.1)))

        old_difficulty = curriculum_learning.current_difficulty
        curriculum_learning.update_performance(episode_reward, episode_success, attention_quality)
        new_difficulty = curriculum_learning.current_difficulty

        if episode % 10 == 9:  # 每10个episode报告一次
            print(f"    Episode {episode+1}: 难度 {old_difficulty:.3f} -> {new_difficulty:.3f}, "
                  f"奖励 {episode_reward:.2f}, 成功: {'是' if episode_success else '否'}")

    # 最终统计
    final_curriculum_stats = curriculum_learning.get_statistics()
    final_stability_stats = stability_manager.get_statistics()

    print(f"\n最终统计:")
    print(f"  课程学习:")
    print(f"    最终难度: {final_curriculum_stats['current_difficulty']:.3f}")
    print(f"    成功率: {final_curriculum_stats['current_success_rate']:.3f}")
    print(f"    平均性能: {final_curriculum_stats['avg_performance']:.3f}")
    print(f"    总调整次数: {final_curriculum_stats['total_adaptations']}")

    print(f"  稳定性管理:")
    print(f"    当前稳定性分数: {final_stability_stats['current_stability_score']:.3f}")
    print(f"    自适应学习率因子: {final_stability_stats['adaptive_lr_factor']:.3f}")
    print(f"    总训练步数: {final_stability_stats['step_count']}")

    print(f"\n增强的训练策略与优化算法测试完成!")
    return {
        'curriculum_learning': curriculum_learning,
        'stability_manager': stability_manager,
        'final_difficulty': curriculum_learning.current_difficulty,
        'final_stability': stability_manager.compute_stability_score()
    }

def test_warehouse_environment():
    """测试仓储环境的基本功能"""
    print("=" * 50)
    print("测试多AGV仓储环境实现")
    print("=" * 50)

    # 创建环境
    env = WarehouseEnvironment()
    print(f"环境创建成功:")
    print(f"  地图尺寸: {env.width} × {env.height}")
    print(f"  AGV数量: {env.num_agvs}")
    print(f"  任务数量: {env.num_tasks}")
    print(f"  货架数量: {env.num_shelves}")

    # 重置环境
    obs = env.reset()
    print(f"\n环境重置成功:")
    print(f"  全局状态 - AGV特征形状: {obs['global_state']['agv_features'].shape}")
    print(f"  全局状态 - 任务特征形状: {obs['global_state']['task_features'].shape}")
    print(f"  局部观察数量: {len(obs['local_observations'])}")

    # 检查AGV初始状态
    print(f"\nAGV初始状态:")
    for i, agv in enumerate(env.agvs):
        print(f"  AGV {i}: 位置({agv.x}, {agv.y}), 载重{agv.current_load}/{agv.capacity}, 状态{agv.status}")

    # 检查任务初始状态
    print(f"\n任务初始状态:")
    for i, task in enumerate(env.tasks[:5]):  # 只显示前5个任务
        grid_value = env.grid[task.y, task.x]
        location_type = "货架" if grid_value == 1 else "通道" if grid_value == 0 else f"其他({grid_value})"
        print(f"  任务 {i}: 位置({task.x}, {task.y}), 重量{task.weight}, 状态{task.status}, 位于{location_type}")
    print(f"  ... (共{len(env.tasks)}个任务)")

    # 验证所有任务是否都在货架上
    tasks_on_shelves = sum(1 for task in env.tasks if env.grid[task.y, task.x] == 1)
    print(f"\n任务位置验证:")
    print(f"  在货架上的任务数量: {tasks_on_shelves}/{len(env.tasks)}")
    if tasks_on_shelves != len(env.tasks):
        print(f"  警告: 有{len(env.tasks) - tasks_on_shelves}个任务不在货架上!")

    # 测试动作掩码
    action_masks = env.get_action_masks()
    print(f"\n动作掩码测试:")
    for i, (high_mask, low_mask) in enumerate(action_masks):
        valid_high = high_mask.sum()
        valid_low = low_mask.sum()
        print(f"  AGV {i}: 有效高层动作{valid_high}/18, 有效低层动作{valid_low}/5")

    # 测试随机动作
    print(f"\n执行随机动作测试:")
    for step in range(3):
        # 生成随机动作
        actions = []
        for i, (high_mask, low_mask) in enumerate(action_masks):
            valid_high_actions = np.where(high_mask)[0]
            valid_low_actions = np.where(low_mask)[0]

            high_action = np.random.choice(valid_high_actions)
            low_action = np.random.choice(valid_low_actions)
            actions.append((high_action, low_action))

        # 执行动作
        obs, rewards, done, info = env.step(actions)

        print(f"  步骤 {step + 1}:")
        print(f"    动作: {actions}")
        print(f"    奖励: {[f'{r:.2f}' for r in rewards]}")
        print(f"    信息: {info}")

        # 更新动作掩码
        action_masks = env.get_action_masks()

        if done:
            print(f"    回合结束!")
            break

    # 获取性能指标
    metrics = env.get_performance_metrics()
    print(f"\n性能指标:")
    for key, value in metrics.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")

    # 可视化环境
    print(f"\n生成环境可视化...")
    try:
        env.render(save_path='warehouse_test.png')
        print(f"  可视化已保存到 warehouse_test.png")
    except Exception as e:
        print(f"  可视化失败: {e}")

    print(f"\n多AGV仓储环境测试完成!")
    return env

if __name__ == "__main__":
    # 运行增强的训练策略与优化算法测试
    test_enhanced_optimization = test_enhanced_training_optimization()

    print("\n" + "=" * 60)

    # 运行训练策略与优化算法测试
    test_optimization_components = test_training_optimization()

    print("\n" + "=" * 60)

    # 运行MAPPO训练目标函数测试
    test_loss_function, test_trainer = test_mappo_loss_function()

    print("\n" + "=" * 60)

    # 运行集成双层注意力的MAPPO系统测试
    test_mappo_system = test_mappo_with_dual_attention()

    print("\n" + "=" * 60)

    # 运行注意力增强价值网络测试
    test_value_network = test_attention_enhanced_value()

    print("\n" + "=" * 60)

    # 运行注意力增强策略网络测试
    test_policy_network = test_attention_enhanced_policy()

    print("\n" + "=" * 60)

    # 运行双层注意力融合机制测试
    test_integrated_attention = test_dual_attention_fusion()

    print("\n" + "=" * 60)

    # 运行第二层协作感知注意力机制测试
    test_collab_attention = test_collaboration_attention()

    print("\n" + "=" * 60)

    # 运行第一层任务分配注意力机制测试
    test_attention, test_state_processor = test_task_allocation_attention()

    print("\n" + "=" * 60)

    # 运行动作空间和奖励函数测试
    test_env, test_action_space, test_reward_function = test_action_space_and_rewards()

    print("\n" + "=" * 60)

    # 运行基础环境测试
    test_env = test_warehouse_environment()

# ================================
# 第二部分：特征嵌入与状态空间处理
# ================================

class FeatureEmbedding(nn.Module):
    """特征嵌入网络"""

    def __init__(self, input_dim: int, embed_dim: int = 64):
        super().__init__()
        self.embed_dim = embed_dim
        self.embedding = nn.Sequential(
            nn.Linear(input_dim, embed_dim),
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 输入特征 (..., input_dim)
        Returns:
            embedded features (..., embed_dim)
        """
        return self.embedding(x)

class TaskEmbedding(FeatureEmbedding):
    """任务特征嵌入 (5维 -> 64维)"""

    def __init__(self):
        super().__init__(input_dim=5, embed_dim=64)

class AGVEmbedding(FeatureEmbedding):
    """AGV特征嵌入 (6维 -> 64维)"""

    def __init__(self):
        super().__init__(input_dim=6, embed_dim=64)

class StateProcessor:
    """状态空间处理器"""

    def __init__(self, device='cpu'):
        self.device = device
        self.task_embedding = TaskEmbedding().to(device)
        self.agv_embedding = AGVEmbedding().to(device)

    def process_global_state(self, global_state: Dict) -> torch.Tensor:
        """处理全局状态

        Args:
            global_state: 包含agv_features (4,6), task_features (16,5)

        Returns:
            global_state_tensor: (20, 64) - 4个AGV + 16个任务的嵌入特征
        """
        # AGV特征嵌入
        agv_features = torch.FloatTensor(global_state['agv_features']).to(self.device)  # (4, 6)
        agv_embedded = self.agv_embedding(agv_features)  # (4, 64)

        # 任务特征嵌入
        task_features = torch.FloatTensor(global_state['task_features']).to(self.device)  # (16, 5)
        task_embedded = self.task_embedding(task_features)  # (16, 64)

        # 拼接全局状态
        global_embedded = torch.cat([agv_embedded, task_embedded], dim=0)  # (20, 64)

        return global_embedded

    def process_local_observations(self, local_observations: List[Dict]) -> List[Dict]:
        """处理局部观察

        Args:
            local_observations: 每个AGV的局部观察列表

        Returns:
            processed_observations: 处理后的局部观察列表
        """
        processed = []

        for obs in local_observations:
            # 自身特征嵌入
            self_features = torch.FloatTensor(obs['self_features']).unsqueeze(0).to(self.device)  # (1, 6)
            self_embedded = self.agv_embedding(self_features).squeeze(0)  # (64,)

            # 可见任务嵌入
            visible_tasks = obs['visible_tasks']  # (N, 5)
            if len(visible_tasks) > 0:
                visible_tasks_tensor = torch.FloatTensor(visible_tasks).to(self.device)
                visible_tasks_embedded = self.task_embedding(visible_tasks_tensor)  # (N, 64)
            else:
                visible_tasks_embedded = torch.zeros(0, 64).to(self.device)

            # 附近AGV嵌入
            nearby_agvs = torch.FloatTensor(obs['nearby_agvs']).to(self.device)  # (3, 6)
            nearby_agvs_embedded = self.agv_embedding(nearby_agvs)  # (3, 64)

            processed_obs = {
                'self_embedded': self_embedded,  # (64,)
                'visible_tasks_embedded': visible_tasks_embedded,  # (N, 64)
                'nearby_agvs_embedded': nearby_agvs_embedded  # (3, 64)
            }
            processed.append(processed_obs)

        return processed

# ================================
# 第三部分：双层注意力机制实现
# ================================

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, embed_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert embed_dim % num_heads == 0

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5

    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            query: (batch_size, seq_len_q, embed_dim)
            key: (batch_size, seq_len_k, embed_dim)
            value: (batch_size, seq_len_v, embed_dim)
            mask: (batch_size, seq_len_q, seq_len_k) or None

        Returns:
            output: (batch_size, seq_len_q, embed_dim)
            attention_weights: (batch_size, num_heads, seq_len_q, seq_len_k)
        """
        batch_size, seq_len_q, _ = query.shape
        seq_len_k = key.shape[1]

        # 线性变换
        Q = self.q_proj(query)  # (batch_size, seq_len_q, embed_dim)
        K = self.k_proj(key)    # (batch_size, seq_len_k, embed_dim)
        V = self.v_proj(value)  # (batch_size, seq_len_v, embed_dim)

        # 重塑为多头
        Q = Q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale  # (batch_size, num_heads, seq_len_q, seq_len_k)

        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            scores.masked_fill_(mask == 0, float('-inf'))

        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        output = torch.matmul(attention_weights, V)  # (batch_size, num_heads, seq_len_q, head_dim)

        # 重塑输出
        output = output.transpose(1, 2).contiguous().view(batch_size, seq_len_q, self.embed_dim)
        output = self.out_proj(output)

        return output, attention_weights

class TaskAllocationAttention(nn.Module):
    """第一层：任务分配注意力机制"""

    def __init__(self, embed_dim: int = 64, num_heads: int = 8, top_k: int = 8):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.top_k = top_k

        # 多头注意力
        self.attention = MultiHeadAttention(embed_dim, num_heads)

        # 约束权重参数
        self.lambda_d = 2.0  # 距离约束权重
        self.lambda_c = 1.0  # 载重约束权重
        self.lambda_p = 0.5  # 优先级约束权重
        self.lambda_t = 0.3  # 时间约束权重

        # 时序一致性参数
        self.lambda_temporal = 0.1
        self.prev_attention_weights = None

        # 层归一化和残差连接
        self.layer_norm = nn.LayerNorm(embed_dim)

    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                constraints: Optional[Dict] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            agv_features: AGV特征 (batch_size, num_agvs, embed_dim)
            task_features: 任务特征 (batch_size, num_tasks, embed_dim)
            constraints: 约束信息字典

        Returns:
            output: 注意力输出 (batch_size, num_agvs, embed_dim)
            attention_weights: 注意力权重 (batch_size, num_agvs, num_tasks)
        """
        batch_size, num_agvs, _ = agv_features.shape
        num_tasks = task_features.shape[1]

        # 计算原始注意力分数
        output, raw_attention = self.attention(
            query=agv_features,      # (batch_size, num_agvs, embed_dim)
            key=task_features,       # (batch_size, num_tasks, embed_dim)
            value=task_features      # (batch_size, num_tasks, embed_dim)
        )

        # 提取注意力权重 (平均多头)
        attention_weights = raw_attention.mean(dim=1)  # (batch_size, num_agvs, num_tasks)

        # 应用约束增强
        if constraints is not None:
            attention_weights = self._apply_constraints(attention_weights, constraints)

        # Top-K稀疏化
        attention_weights = self._apply_topk_sparsity(attention_weights)

        # 时序一致性约束
        if self.prev_attention_weights is not None:
            temporal_loss = self._compute_temporal_consistency_loss(attention_weights)
        else:
            temporal_loss = 0.0

        # 更新历史注意力权重
        self.prev_attention_weights = attention_weights.detach().clone()

        # 重新计算输出（使用约束后的权重）
        # 扩展权重维度以匹配value
        expanded_weights = attention_weights.unsqueeze(-1)  # (batch_size, num_agvs, num_tasks, 1)
        task_features_expanded = task_features.unsqueeze(1).expand(-1, num_agvs, -1, -1)  # (batch_size, num_agvs, num_tasks, embed_dim)

        # 加权求和
        weighted_output = (expanded_weights * task_features_expanded).sum(dim=2)  # (batch_size, num_agvs, embed_dim)

        # 残差连接和层归一化
        output = self.layer_norm(agv_features + weighted_output)

        return output, attention_weights

    def _apply_constraints(self, attention_weights: torch.Tensor, constraints: Dict) -> torch.Tensor:
        """应用约束增强"""
        batch_size, num_agvs, num_tasks = attention_weights.shape

        # 距离约束
        if 'distances' in constraints:
            distances = constraints['distances']  # (batch_size, num_agvs, num_tasks)
            distance_constraint = -self.lambda_d * distances
            attention_weights = attention_weights + distance_constraint

        # 载重约束
        if 'capacity_valid' in constraints:
            capacity_mask = constraints['capacity_valid']  # (batch_size, num_agvs, num_tasks)
            capacity_constraint = torch.where(capacity_mask,
                                             torch.tensor(self.lambda_c),
                                             torch.tensor(-self.lambda_c))
            attention_weights = attention_weights + capacity_constraint

        # 可用性约束
        if 'task_available' in constraints:
            availability_mask = constraints['task_available']  # (batch_size, num_tasks)
            availability_mask = availability_mask.unsqueeze(1).expand(-1, num_agvs, -1)
            attention_weights = attention_weights.masked_fill(~availability_mask, float('-inf'))

        return attention_weights

    def _apply_topk_sparsity(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """应用Top-K稀疏化"""
        batch_size, num_agvs, num_tasks = attention_weights.shape
        k = min(self.top_k, num_tasks)

        # 对每个AGV选择Top-K任务
        topk_values, topk_indices = torch.topk(attention_weights, k, dim=-1)

        # 创建稀疏掩码
        sparse_weights = torch.full_like(attention_weights, float('-inf'))
        for b in range(batch_size):
            for a in range(num_agvs):
                sparse_weights[b, a, topk_indices[b, a]] = topk_values[b, a]

        # 重新归一化
        sparse_weights = F.softmax(sparse_weights, dim=-1)

        return sparse_weights

    def _compute_temporal_consistency_loss(self, current_weights: torch.Tensor) -> torch.Tensor:
        """计算时序一致性损失"""
        if self.prev_attention_weights is None:
            return torch.tensor(0.0)

        # L2距离
        temporal_loss = F.mse_loss(current_weights, self.prev_attention_weights)
        return self.lambda_temporal * temporal_loss
